# 日报数据导出功能说明

## 概述

本功能为ExcelController的/export接口新增了两种业务场景的数据导出：
1. 门店日报数据导出 (dailyAiStatistics)
2. 集团日报数据导出 (dailyAiStatisticsGroup)

## 接口说明

### 请求地址
```
POST /excel/export
```

### 请求参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
|--------|------|----------|------|--------|
| business_type | string | 是 | 业务类型 | dailyAiStatistics 或 dailyAiStatisticsGroup |
| request_json | string | 是 | 请求参数JSON | {"startDate": "2025-08-06", "endDate": "2025-08-08"} |

### 业务类型说明

#### 1. 门店日报数据导出 (dailyAiStatistics)
- **业务类型**: `dailyAiStatistics`
- **数据范围**: 当前用户所属酒店的每日数据
- **Excel表头**: 
  - 日期
  - 使用房间数
  - AI语音通话数
  - 平均通话时长（秒）
  - 文字对话数
  - 生成工单
  - AI解决数
  - 人工回拨数
  - 处理时长
  - 超时工单数
  - 投诉工单数

#### 2. 集团日报数据导出 (dailyAiStatisticsGroup)
- **业务类型**: `dailyAiStatisticsGroup`
- **数据范围**: 所有酒店的聚合数据
- **Excel表头**:
  - 酒店
  - 使用房间数
  - AI语音通话数
  - 平均通话时长（秒）
  - 文字对话数
  - 生成工单数
  - AI解决数
  - 人工回拨数
  - 处理时长
  - 超时工单数
  - 投诉工单数

## 请求示例

### 门店日报导出
```json
{
  "business_type": "dailyAiStatistics",
  "request_json": "{\"startDate\": \"2025-08-06\", \"endDate\": \"2025-08-08\"}"
}
```

### 集团日报导出
```json
{
  "business_type": "dailyAiStatisticsGroup",
  "request_json": "{\"startDate\": \"2025-08-06\", \"endDate\": \"2025-08-08\"}"
}
```

## 数据来源

数据来源于 `hds_hotel_daily_ai_statistics` 表，主要字段映射如下：

| Excel列名 | 数据库字段 | 说明 |
|-----------|------------|------|
| 使用房间数 | room_use_count | 使用房间数 |
| AI语音通话数 | ai_call_count | AI语音通话数 |
| 平均通话时长（秒） | avg_call_duration_seconds | 平均通话时长（秒） |
| 文字对话数 | text_dialogue_count | 文字对话数 |
| 生成工单/生成工单数 | ticket_count | 生成工单 |
| AI解决数 | ai_solve_count | AI解决数 |
| 人工回拨数 | return_call_count | 人工回拨数 |
| 处理时长 | avg_complete_duration_seconds | 处理时长（秒） |
| 超时工单数 | overdue_count | 超时工单数 |
| 投诉工单数 | complaint_ticket_count | 投诉工单数 |

## 权限说明

- **门店日报**: 只能导出当前用户所属酒店的数据
- **集团日报**: 可以导出所有酒店的数据（需要相应权限）

## 文件命名规则

- 门店日报: `门店日报数据_yyyyMMdd_HHmmss.xlsx`
- 集团日报: `集团日报数据_yyyyMMdd_HHmmss.xlsx`

## 注意事项

1. 日期格式必须为 `yyyy-MM-dd` 格式
2. 集团日报会按酒店聚合数据，平均值字段取所有日期的平均值
3. 数据查询会自动过滤 `row_status = 1` 的有效记录
4. 门店日报按日期升序排列，集团日报按酒店编码排列

## 技术实现

### 新增文件
1. `HdsHotelDailyAiStatisticsEntity.java` - 数据库实体类
2. `HdsHotelDailyAiStatisticsFieldEnum.java` - 字段枚举
3. `HdsHotelDailyAiStatisticsRepository.java` - 数据仓库接口
4. `DailyAiStatisticsDataExportProcessor.java` - 门店日报导出处理器
5. `DailyAiStatisticsGroupDataExportProcessor.java` - 集团日报导出处理器
6. 相关的请求类和DTO类

### 修改文件
1. `BussinessTypeEnum.java` - 添加新的业务类型枚举
2. `ExcelConstant.java` - 添加日报数据导出相关的Excel表头常量

### 测试
运行 `DailyAiStatisticsExportTest` 可以验证功能是否正常工作。
