package com.wormhole.hotelds.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @Author：flx
 * @Date：2025/4/8 11:55
 * @Description：CommonProperties
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "wormhole.common.config")
public class CommonProperties {

    /**
     * 默认品牌logo
     */
    private String defaultHotelLogo;

    /**
     * 房间对象存储key
     */
    private String roomObjectKey;

    /**
     * 设备位置对象存储key
     */
    private String devicePositionObjectKey;

    /**
     * 设备对象存储key
     */
    private String deviceObjectKey;

    /**
     * 员工导入模版存储key(运营端)
     */
    private String employeeObjectKey;


    private String merchantHost = "https://test-merchant-web.delonix.group";

    /**
     * 门店员工导入模版存储key
     */
    private String hotelEmployeeObjectKey;
}
