package com.wormhole.hotelds.excel.dailyaistatistics;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.model.entity.HdsHotelDailyAiStatisticsEntity;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.enums.HdsHotelDailyAiStatisticsFieldEnum;
import com.wormhole.hotelds.admin.repository.HdsHotelDailyAiStatisticsRepository;
import com.wormhole.hotelds.excel.DataExportProcessor;
import com.wormhole.hotelds.excel.ExportContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Author: AI Assistant
 * @Date: 2025-08-15
 * @Description: 门店日报数据导出处理器
 */
@Slf4j
@Component
public class DailyAiStatisticsDataExportProcessor implements DataExportProcessor<DailyAiStatisticsExportDTO, DailyAiStatisticsExportReq> {

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private HdsHotelDailyAiStatisticsRepository repository;

    @Resource
    private ObjectMapper objectMapper;

    @Override
    public String getBusinessType() {
        return BussinessTypeEnum.DAILY_AI_STATISTICS.getBusinessType();
    }

    @Override
    public List<String> getExcelHeaders() {
        return Arrays.asList(
                "日期",
                "使用房间数",
                "AI语音通话数",
                "平均通话时长（秒）",
                "文字对话数",
                "生成工单",
                "AI解决数",
                "人工回拨数",
                "处理时长",
                "超时工单数",
                "投诉工单数"
        );
    }

    @Override
    public Flux<DailyAiStatisticsExportDTO> queryData(DailyAiStatisticsExportReq request, ExportContext context) {
        return parseRequestJson(request)
                .flatMapMany(parsedReq -> {
                    // 获取当前用户的酒店编码
                    return HeaderUtils.getHeaderInfo()
                            .flatMapMany(headerInfo -> {
                                String hotelCode = headerInfo.getHotelCode();
                                if (StringUtils.isBlank(hotelCode)) {
                                    return Flux.error(new BusinessException(ResultCode.INVALID_PARAMETER, "无法获取酒店编码"));
                                }

                                // 构建查询条件
                                Criteria criteria = Criteria.where(HdsHotelDailyAiStatisticsFieldEnum.hotel_code.name()).is(hotelCode)
                                        .and(HdsHotelDailyAiStatisticsFieldEnum.row_status.name()).is(1);

                                // 添加日期范围条件
                                if (StringUtils.isNotBlank(parsedReq.getStartDate())) {
                                    LocalDate startDate = LocalDate.parse(parsedReq.getStartDate());
                                    criteria = criteria.and(HdsHotelDailyAiStatisticsFieldEnum.business_date.name()).greaterThanOrEquals(startDate);
                                }
                                if (StringUtils.isNotBlank(parsedReq.getEndDate())) {
                                    LocalDate endDate = LocalDate.parse(parsedReq.getEndDate());
                                    criteria = criteria.and(HdsHotelDailyAiStatisticsFieldEnum.business_date.name()).lessThanOrEquals(endDate);
                                }

                                // 执行查询
                                Query query = Query.query(criteria)
                                        .sort(Sort.by(Sort.Direction.ASC, HdsHotelDailyAiStatisticsFieldEnum.business_date.name()));

                                return r2dbcEntityTemplate.select(query, HdsHotelDailyAiStatisticsEntity.class)
                                        .map(DailyAiStatisticsExportDTO::fromEntity);
                            });
                });
    }

    @Override
    public List<String> convertToRow(DailyAiStatisticsExportDTO data) {
        List<String> row = new ArrayList<>();
        row.add(data.getDate() != null ? data.getDate() : "");
        row.add(data.getRoomUseCount() != null ? data.getRoomUseCount().toString() : "0");
        row.add(data.getAiCallCount() != null ? data.getAiCallCount().toString() : "0");
        row.add(data.getAvgCallDurationSeconds() != null ? String.format("%.2f", data.getAvgCallDurationSeconds()) : "0.00");
        row.add(data.getTextDialogueCount() != null ? data.getTextDialogueCount().toString() : "0");
        row.add(data.getTicketCount() != null ? data.getTicketCount().toString() : "0");
        row.add(data.getAiSolveCount() != null ? data.getAiSolveCount().toString() : "0");
        row.add(data.getReturnCallCount() != null ? data.getReturnCallCount().toString() : "0");
        row.add(data.getAvgCompleteDurationSeconds() != null ? String.format("%.2f", data.getAvgCompleteDurationSeconds()) : "0.00");
        row.add(data.getOverdueCount() != null ? data.getOverdueCount().toString() : "0");
        row.add(data.getComplaintTicketCount() != null ? data.getComplaintTicketCount().toString() : "0");
        return row;
    }

    @Override
    public String getExportFileName(DailyAiStatisticsExportReq request) {
        String dateStr = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss").format(LocalDateTime.now());
        return "门店日报数据_" + dateStr + ".xlsx";
    }

    @Override
    public Class<DailyAiStatisticsExportReq> getRequestClass() {
        return DailyAiStatisticsExportReq.class;
    }

    /**
     * 解析请求JSON参数
     */
    private reactor.core.publisher.Mono<DailyAiStatisticsExportReq> parseRequestJson(DailyAiStatisticsExportReq request) {
        return reactor.core.publisher.Mono.fromCallable(() -> {
            if (StringUtils.isBlank(request.getRequestJson())) {
                throw new BusinessException(ResultCode.INVALID_PARAMETER, "请求参数不能为空");
            }

            try {
                JsonNode jsonNode = objectMapper.readTree(request.getRequestJson());
                String startDate = jsonNode.has("startDate") ? jsonNode.get("startDate").asText() : null;
                String endDate = jsonNode.has("endDate") ? jsonNode.get("endDate").asText() : null;

                request.setStartDate(startDate);
                request.setEndDate(endDate);

                return request;
            } catch (JsonProcessingException e) {
                log.error("解析请求JSON失败: {}", e.getMessage(), e);
                throw new BusinessException(ResultCode.INVALID_PARAMETER, "请求参数格式错误");
            }
        });
    }
}
