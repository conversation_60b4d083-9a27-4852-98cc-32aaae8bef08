package com.wormhole.hotelds.excel.device;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.wormhole.common.model.entity.BaseEntity;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.aop.LoggingContext;
import com.wormhole.hotelds.admin.constant.ExcelConstant;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.enums.DeviceEventTypeEnum;
import com.wormhole.hotelds.admin.model.enums.OperationTypeEnum;
import com.wormhole.hotelds.admin.repository.DeviceInfoRepository;
import com.wormhole.hotelds.admin.repository.DeviceLogRepository;
import com.wormhole.hotelds.admin.repository.DeviceModelRepository;
import com.wormhole.hotelds.admin.repository.DeviceRepository;
import com.wormhole.hotelds.admin.service.DeviceService;
import com.wormhole.hotelds.core.enums.DeviceStatusEnum;
import com.wormhole.hotelds.core.model.entity.HdsDeviceEntity;
import com.wormhole.hotelds.core.model.entity.HdsDeviceInfoEntity;
import com.wormhole.hotelds.core.model.entity.HdsDeviceLogEntity;
import com.wormhole.hotelds.core.model.entity.HdsDeviceModelEntity;
import com.wormhole.hotelds.excel.DataImportProcessor;
import com.wormhole.hotelds.excel.EntityCollection;
import com.wormhole.hotelds.excel.ImportContext;
import com.wormhole.hotelds.util.OperationLogUtil;
import com.wormhole.hotelds.util.SimpleDateUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple3;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author：flx
 * @Date：2025/4/7 17:50
 * @Description：设备
 */
@Slf4j
@Component
public class DeviceDataImportProcessorV2 implements DataImportProcessor<DeviceImportDTO, EntityCollection> {

    @Resource
    private DeviceModelRepository deviceModelRepository;

    @Resource
    private DeviceRepository deviceRepository;

    @Resource
    private DeviceInfoRepository deviceInfoRepository;

    @Resource
    private DeviceLogRepository deviceLogRepository;

    @Resource
    private TransactionalOperator transactionalOperator;

    @Resource
    private OperationLogUtil operationLogUtil;

    @Override
    public String getBusinessType() {
        return BussinessTypeEnum.DEVICE.getBusinessType() + "_v2";
    }

    @Override
    public Set<String> getRequiredHeaders(ImportContext context) {
        return new HashSet<>(Arrays.asList(
                ExcelConstant.FIELD_DEVICE_SN,
                ExcelConstant.FIELD_DEVICE_IMEI,
                ExcelConstant.FIELD_PURCHASE_TIME,
                ExcelConstant.FIELD_WARRANTY_START_TIME,
                ExcelConstant.FIELD_WARRANTY_END_TIME,
                ExcelConstant.FIELD_REMARK
        ));
    }

    @Override
    public Flux<DeviceImportDTO> validateData(Flux<DeviceImportDTO> data, ImportContext context) {
        Long businessId = context.getBusinessId();
        if (Objects.isNull(businessId)) {
            context.addRowError(0, "数据验证过程发生错误：设备类型id不能为空");
            return Flux.empty();
        }
        return deviceModelRepository.existsById(businessId)
                .flatMapMany(exist -> {
                    if (!exist) {
                        context.addRowError(0, "数据验证过程发生错误：" + "设备类型不存在");
                        return Flux.empty();
                    }
                    return data.collectList()
                            .flatMapMany(dtoList -> {
                                // 1. 设置总数
                                context.setTotalCount(dtoList.size());

                                // 2. 检查当前批次内的重复
                                Map<String, List<DeviceImportDTO>> duplicatesInBatch = checkDuplicatesInBatch(dtoList);

                                // 3. 获取所有设备SN
                                Set<String> allDeviceSns = dtoList.stream()
                                        .map(DeviceImportDTO::getDeviceSn)
                                        .filter(StringUtils::isNotBlank)
                                        .collect(Collectors.toSet());

                                List<String> allImeis = dtoList.stream()
                                        .map(DeviceImportDTO::getImei)
                                        .filter(StringUtils::isNotBlank)
                                        .collect(Collectors.toList());


                                // 4. 如果没有有效的设备SN，直接返回空结果
                                if (allDeviceSns.isEmpty() && allImeis.isEmpty()) {
                                    return Flux.empty();
                                }

                                // 5. 查询数据库中已存在的设备SN
                                Mono<Set<String>> existingSnsMono = Mono.just(Collections.emptySet());
                                if (CollectionUtils.isNotEmpty(allDeviceSns)) {
                                    existingSnsMono = deviceRepository.findByDeviceSnInAndModelId(allDeviceSns, businessId)
                                            .map(hdsDeviceEntity ->
                                                    hdsDeviceEntity.getModelId() + "_" +
                                                            hdsDeviceEntity.getDeviceSn()).collect(Collectors.toSet());
                                }

                                Mono<Set<String>> existingImeisMono = Mono.just(Collections.emptySet());
                                if (CollectionUtils.isNotEmpty(allImeis)) {
                                    existingImeisMono = deviceRepository.findByImeiInAndModelId(allDeviceSns, businessId)
                                            .map(hdsDeviceEntity ->
                                                    hdsDeviceEntity.getModelId() + "_" +
                                                            hdsDeviceEntity.getDeviceSn()).collect(Collectors.toSet());
                                }

                                return Mono.zip(existingSnsMono, existingImeisMono)
                                        .flatMapMany(tuple -> {
                                            Set<String> existingSns = tuple.getT1();
                                            Set<String> existingImeis = tuple.getT2();

                                            // 处理每条数据
                                            return Flux.fromIterable(dtoList)
                                                    .filterWhen(dto -> validateDevice(dto, existingSns, existingImeis,
                                                            duplicatesInBatch, context));
                                        });
                            })
                            .doOnError(error -> {
                                log.error("数据验证过程发生错误: {}", error.getMessage(), error);
                                context.addRowError(0, "数据验证过程发生错误：" + error.getMessage());
                            });
                });
    }

    /**
     * 检查批次内的重复数据
     *
     * @return Map<String, List < DeviceImportDTO>> key为设备唯一标识，value为重复的记录
     */
    private Map<String, List<DeviceImportDTO>> checkDuplicatesInBatch(List<DeviceImportDTO> dtoList) {
        Map<String, List<DeviceImportDTO>> duplicates = new HashMap<>();

        Map<String, List<DeviceImportDTO>> snDuplicates = dtoList.stream()
                .filter(dto -> StringUtils.isNotBlank(dto.getDeviceSn()))
                .collect(Collectors.groupingBy(
                        DeviceImportDTO::getDeviceSn,
                        Collectors.toList()
                ));

        // 检查imei + modelCode重复
        Map<String, List<DeviceImportDTO>> imeiDuplicates = dtoList.stream()
                .filter(dto -> StringUtils.isNotBlank(dto.getImei()))
                .collect(Collectors.groupingBy(
                        DeviceImportDTO::getImei,
                        Collectors.toList()
                ));

        // 合并重复结果
        duplicates.putAll(snDuplicates.entrySet().stream()
                .filter(entry -> entry.getValue().size() > 1)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));

        duplicates.putAll(imeiDuplicates.entrySet().stream()
                .filter(entry -> entry.getValue().size() > 1)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));

        return duplicates;
    }

    /**
     * 验证单个设备
     */
    private Mono<Boolean> validateDevice(
            DeviceImportDTO dto,
            Set<String> existingSns,
            Set<String> existingImeiKeys,
            Map<String, List<DeviceImportDTO>> duplicatesInBatch,
            ImportContext context) {

        // 1. 基础格式验证
        if (!validateBasicFields(dto, context)) {
            return Mono.just(false);
        }

        // 2. 重复性验证
        if (!validateDuplicates(dto, existingSns, existingImeiKeys, duplicatesInBatch, context)) {
            return Mono.just(false);
        }

        // 3. 业务规则验证
        return Mono.just(true);
    }

    /**
     * 基础字段验证
     */
    private boolean validateBasicFields(DeviceImportDTO dto, ImportContext context) {
        for (DeviceValidationRule rule : DeviceValidationRule.values()) {
            String value = rule.getValueGetter().apply(dto);

            // 必填字段验证
            if (rule.isRequired() && StringUtils.isBlank(value)) {
                context.addRowError(dto.getRowNum(),
                        String.format("%s不能为空", rule.getFieldName()));
                return false;
            }

            // 长度验证
            if (StringUtils.isNotBlank(value) && rule.getMaxLength() != null
                    && value.length() > rule.getMaxLength()) {
                context.addRowError(dto.getRowNum(),
                        String.format("%s长度不能超过%d个字符",
                                rule.getFieldName(), rule.getMaxLength()));
                return false;
            }
        }

        if (StringUtils.isBlank(dto.getDeviceSn()) && StringUtils.isBlank(dto.getImei())) {
            context.addRowError(dto.getRowNum(), "设备SN和IMEI不能同时为空");
        }

        // 日期格式验证
        return validateDates(dto, context);
    }

    /**
     * 日期格式验证
     */
    private boolean validateDates(DeviceImportDTO dto, ImportContext context) {
        try {
            LocalDateTime warrantyStart = SimpleDateUtils.slashStringToStartDateTime(dto.getWarrantyStart());
            LocalDateTime warrantyEnd = SimpleDateUtils.slashStringToStartDateTime(dto.getWarrantyEnd());
            LocalDateTime purchaseTime = SimpleDateUtils.slashStringToStartDateTime(dto.getPurchaseTime());

            if (warrantyStart == null || warrantyEnd == null) {
                context.addRowError(dto.getRowNum(), "保修时间格式不正确，请使用yyyy-MM-dd格式");
                return false;
            }

            if (warrantyEnd.isBefore(warrantyStart)) {
                context.addRowError(dto.getRowNum(), "保修结束时间不能早于开始时间");
                return false;
            }

            if (purchaseTime == null) {
                context.addRowError(dto.getRowNum(), "采购时间格式不正确，请使用yyyy-MM-dd格式");
                return false;
            }

            return true;
        } catch (Exception e) {
            context.addRowError(dto.getRowNum(), "保修时间格式不正确，请使用yyyy-MM-dd格式");
            return false;
        }
    }

    /**
     * 重复性验证
     */
    private boolean validateDuplicates(
            DeviceImportDTO dto,
            Set<String> existingSnKeys,
            Set<String> existingImeiKeys,
            Map<String, List<DeviceImportDTO>> duplicatesInBatch,
            ImportContext context) {
        Long modelId = dto.getModelId();
        String snKey = modelId + "_" + dto.getDeviceSn();
        String imeiKey = modelId + "_" + dto.getImei();

        // 检查数据库中已存在的记录
        if (StringUtils.isNotBlank(dto.getDeviceSn())) {
            if (existingSnKeys.contains(snKey)) {
                context.addRowError(dto.getRowNum(),
                        String.format("设备SN[%s]在型号[%s]下已存在",
                                dto.getDeviceSn(), dto.getModelId()));
                return false;
            }
        }

        if (StringUtils.isNotBlank(dto.getImei())) {
            if (existingImeiKeys.contains(imeiKey)) {
                context.addRowError(dto.getRowNum(),
                        String.format("设备IMEI[%s]在型号[%s]下已存在",
                                dto.getImei(), modelId));
                return false;
            }
        }

        // 检查批次内的重复
        if (StringUtils.isNotBlank(dto.getDeviceSn())) {
            List<DeviceImportDTO> snDuplicates = duplicatesInBatch.get(snKey);
            if (snDuplicates != null && snDuplicates.get(0) != dto) {
                context.addRowError(dto.getRowNum(),
                        String.format("设备SN[%s]在型号[%s]下在本次导入中重复，已采用第%d行的数据",
                                dto.getDeviceSn(), modelId, snDuplicates.get(0).getRowNum()));
                return false;
            }
        }

        if (StringUtils.isNotBlank(dto.getImei())) {
            List<DeviceImportDTO> imeiDuplicates = duplicatesInBatch.get(imeiKey);
            if (imeiDuplicates != null && imeiDuplicates.get(0) != dto) {
                context.addRowError(dto.getRowNum(),
                        String.format("设备IMEI[%s]在型号[%s]下在本次导入中重复，已采用第%d行的数据",
                                dto.getImei(), modelId, imeiDuplicates.get(0).getRowNum()));
                return false;
            }
        }

        return true;
    }

    @Override
    public Flux<DeviceImportDTO> convertData(Flux<Map<String, String>> rawData, ImportContext context) {
        return rawData.map(map -> DeviceImportDTO.builder()
                .modelId(context.getBusinessId())
                .deviceSn(map.get(ExcelConstant.FIELD_DEVICE_SN))
                .imei(map.get(ExcelConstant.FIELD_DEVICE_IMEI))
                .warrantyStart(map.get(ExcelConstant.FIELD_WARRANTY_START_TIME))
                .warrantyEnd(map.get(ExcelConstant.FIELD_WARRANTY_END_TIME))
                .purchaseTime(map.get(ExcelConstant.FIELD_PURCHASE_TIME))
                .remark(map.get(ExcelConstant.FIELD_REMARK))
                .rowNum(Integer.parseInt(map.getOrDefault("_excelRowNum", "0")))
                .build());
    }

    @Override
    public Flux<EntityCollection> toEntities(Flux<DeviceImportDTO> data, ImportContext context) {
        return data.collectList()
                // 2. 调用批量处理方法
                .flatMap(dtoList -> toBatchEntities(dtoList, context))
                // 3. 将List转换为Flux
                .flatMapMany(Flux::fromIterable);
    }

    @Override
    public Mono<List<EntityCollection>> toBatchEntities(List<DeviceImportDTO> dtoList, ImportContext context) {
        if (dtoList == null || dtoList.isEmpty()) {
            return Mono.just(Collections.emptyList());
        }
        HeaderUtils.HeaderInfo headerInfo = context.getHeaderInfo();
        Long businessId = context.getBusinessId();

        // 2. 批量查询设备型号
        return deviceModelRepository.findById(businessId)
                .flatMap(hdsDeviceModelEntity -> {
                    List<EntityCollection> collections = new ArrayList<>();

                    // 4. 构建实体集合
                    for (DeviceImportDTO dto : dtoList) {
                        try {

                            // 构建设备信息实体
                            HdsDeviceInfoEntity deviceInfo = buildDeviceInfo(dto, hdsDeviceModelEntity, headerInfo);

                            // 构建设备实体
                            HdsDeviceEntity device = buildDevice(dto, hdsDeviceModelEntity, headerInfo);

                            // 更新设备型号库存
                            updateDeviceModelInventory(hdsDeviceModelEntity, headerInfo);

                            // 构建设备日志
                            HdsDeviceLogEntity deviceLog = buildDeviceLog(device, hdsDeviceModelEntity, headerInfo);

                            // 创建实体集合
                            EntityCollection collection = EntityCollection.builder()
                                    .entities(new HashMap<>())
                                    .rowNum(dto.getRowNum())
                                    .build();

                            // 添加实体到集合
                            collection.addEntity("deviceInfo", deviceInfo);
                            collection.addEntity("device", device);
                            collection.addEntity("deviceModel", hdsDeviceModelEntity);
                            collection.addEntity("deviceLog", deviceLog);

                            collections.add(collection);
                        } catch (Exception e) {
                            log.error("实体转换失败: dto={}, error={}", dto, e.getMessage());
                            context.addRowError(dto.getRowNum(), String.format("实体转换失败: dto=%s, error=%s", dto, e.getMessage()));
                        }
                    }
                    return Mono.just(collections);
                });
    }

    @Override
    public Mono<Integer> saveData(List<EntityCollection> collections, ImportContext context) {
        return Flux.fromIterable(collections)
                .flatMap(collection -> {
                    // 获取各个实体
                    HdsDeviceInfoEntity deviceInfo = collection.getEntity("deviceInfo");
                    HdsDeviceEntity device = collection.getEntity("device");
                    HdsDeviceModelEntity deviceModel = collection.getEntity("deviceModel");

                    ExcelSaveReq excelSaveReq = buildExcelSaveReq(deviceInfo, device, deviceModel);

                    // 创建事务操作列表
                    Mono<Tuple3<HdsDeviceInfoEntity, HdsDeviceEntity, HdsDeviceModelEntity>> tuple3Mono = DeviceService.tuple3Mono(deviceInfo, device, deviceModel, deviceInfoRepository, deviceRepository, deviceModelRepository);

                    // 执行事务，并处理成功/失败情况
                    return transactionalOperator.transactional(tuple3Mono)
                            .doOnSuccess(result -> {
                                // 保存成功，记录日志并增加成功计数
                                handleSuccess(result, excelSaveReq, context);
                                context.incrementSuccessCount();
                            })
                            .thenReturn(true)
                            .onErrorResume(e -> {
                                // 保存失败，记录错误信息但继续处理其他记录
                                handleError(e, excelSaveReq, context);
                                log.error("第{}行设备入库失败: {}", collection.getRowNum(), e.getMessage());
                                context.addRowError(collection.getRowNum(),
                                        String.format("设备[%s]入库失败: %s",
                                                device.getDeviceSn(), e.getMessage()));
                                return Mono.just(false);
                            });
                })
                .count()
                .map(Long::intValue);
    }

    /**
     * 构建Excel保存请求对象
     */
    private ExcelSaveReq buildExcelSaveReq(
            HdsDeviceInfoEntity deviceInfo,
            HdsDeviceEntity device,
            HdsDeviceModelEntity deviceModel) {

        ExcelSaveReq excelSaveReq = new ExcelSaveReq();
        excelSaveReq.setModelId(deviceModel.getId());
        excelSaveReq.setModelName(deviceModel.getModelName());
        excelSaveReq.setDeviceSn(device.getDeviceSn());
        excelSaveReq.setImei(device.getImei());
        excelSaveReq.setWarrantyStart(
                SimpleDateUtils.formatLocalDateTimeToDate(deviceInfo.getWarrantyStart()));
        excelSaveReq.setWarrantyEnd(
                SimpleDateUtils.formatLocalDateTimeToDate(deviceInfo.getWarrantyEnd()));
        excelSaveReq.setRemark(deviceInfo.getRemark());
        return excelSaveReq;
    }

    /**
     * 处理保存成功的情况
     */
    private void handleSuccess(
            Tuple3<HdsDeviceInfoEntity, HdsDeviceEntity, HdsDeviceModelEntity> result,
            ExcelSaveReq excelSaveReq,
            ImportContext context) {

        HeaderUtils.HeaderInfo headerInfo = context.getHeaderInfo();
        Instant startTime = context.getStartTime();
        long executionTime = Duration.between(startTime, Instant.now()).toMillis();

        HdsDeviceInfoEntity deviceInfo = result.getT1();
        HdsDeviceEntity device = result.getT2();
        HdsDeviceModelEntity deviceModel = result.getT3();

        // 构建设备日志
        HdsDeviceLogEntity deviceLog = new HdsDeviceLogEntity();
        deviceLog.setDeviceId(device.getDeviceId());
        deviceLog.setDeviceSn(device.getDeviceSn());
        deviceLog.setEventType(DeviceEventTypeEnum.INBOUND.getCode());
        deviceLog.setEventTime(LocalDateTime.now());
        deviceLog.setCreatedAt(LocalDateTime.now());
        deviceLog.setCreatedBy(deviceInfo.getCreatedBy());
        deviceLog.setRemark(String.format("设备[%s]入库，型号[%s]，型号名称[%s]",
                device.getDeviceSn(), deviceModel.getModelCode(), deviceModel.getModelName()));

        // 保存设备日志
        deviceLogRepository.save(deviceLog)
                .subscribe(
                        success -> log.debug("设备日志保存成功: deviceSn={}", device.getDeviceSn()),
                        error -> log.error("设备日志保存失败: deviceSn={}, error={}",
                                device.getDeviceSn(), error.getMessage())
                );

        LoggingContext loggingContext = getLoggingContext(
                excelSaveReq,
                startTime,
                "SUCCESS",
                null,
                headerInfo
        );
        operationLogUtil.recordSuccess(loggingContext).subscribe();
    }

    /**
     * 创建通用的日志上下文对象
     *
     * @param params     操作参数
     * @param startTime  开始时间
     * @param result     操作结果 ("SUCCESS"/"FAIL")
     * @param errorMsg   错误信息 (失败时使用)
     * @param headerInfo 请求头信息
     * @return 日志上下文对象
     */
    private LoggingContext getLoggingContext(
            Object params,
            Instant startTime,
            String result,
            String errorMsg,
            HeaderUtils.HeaderInfo headerInfo) {

        // 计算执行时间
        long executionTime = Duration.between(startTime, Instant.now()).toMillis();

        // 转换时间格式
        LocalDateTime operTime = LocalDateTime.ofInstant(startTime, ZoneId.systemDefault());

        // 构建并返回日志上下文
        return LoggingContext.builder()
                .headerInfo(headerInfo)
                .businessType(BussinessTypeEnum.DEVICE)
                .operationType(OperationTypeEnum.ADD)
                .oprParams(params)
                .methodName("importExcel")
                .oprTime(operTime)
                .executionTime(executionTime)
                .oprContent("Excel批量导入:设备入库")
                .oprResult(result)
                .errorMsg(errorMsg)
                .build();
    }

    /**
     * 处理保存失败的情况
     */
    private void handleError(Throwable error, ExcelSaveReq req, ImportContext context) {
        HeaderUtils.HeaderInfo headerInfo = context.getHeaderInfo();
        Instant startTime = context.getStartTime();
        LoggingContext loggingContext = getLoggingContext(
                req,
                startTime,
                "FAIL",
                error.getMessage(),
                headerInfo
        );

        // 记录操作日志
        operationLogUtil.recordFail(loggingContext).subscribe();
    }

    // 私有辅助方法
    private HdsDeviceInfoEntity buildDeviceInfo(DeviceImportDTO dto, HdsDeviceModelEntity hdsDeviceModelEntity,
                                                HeaderUtils.HeaderInfo headerInfo) {
        HdsDeviceInfoEntity entity = new HdsDeviceInfoEntity();
        entity.setDeviceSn(dto.getDeviceSn());
        entity.setImei(dto.getImei());
        entity.setModelId(hdsDeviceModelEntity.getId());
        entity.setWarrantyStart(SimpleDateUtils.slashStringToStartDateTime(dto.getWarrantyStart()));
        entity.setWarrantyEnd(SimpleDateUtils.slashStringToStartDateTime(dto.getWarrantyEnd()));
        entity.setStorageTime(LocalDateTime.now());
        entity.setPurchaseTime(SimpleDateUtils.slashStringToStartDateTime(dto.getPurchaseTime()));
        entity.setRemark(dto.getRemark());
        String deviceAppType = hdsDeviceModelEntity.getDeviceAppType();
        entity.setDeviceAppType(deviceAppType);
        setAuditFields(entity, headerInfo);
        return entity;
    }

    private HdsDeviceEntity buildDevice(DeviceImportDTO dto, HdsDeviceModelEntity hdsDeviceModelEntity,
                                        HeaderUtils.HeaderInfo headerInfo) {
        HdsDeviceEntity entity = new HdsDeviceEntity();
        entity.setModelId(hdsDeviceModelEntity.getId());
        entity.setDeviceSn(dto.getDeviceSn());
        entity.setImei(dto.getImei());
        entity.setDeviceStatus(DeviceStatusEnum.PENDING_ACTIVATION.getCode());
        entity.setRemark(dto.getRemark());
        String deviceAppType = hdsDeviceModelEntity.getDeviceAppType();
        entity.setDeviceAppType(deviceAppType);
        setAuditFields(entity, headerInfo);
        return entity;
    }

    private void updateDeviceModelInventory(HdsDeviceModelEntity model, HeaderUtils.HeaderInfo headerInfo) {
        model.setTotalInventory(model.getTotalInventory() + 1);
        model.setInventory(model.getInventory() == null ? 1 : model.getInventory() + 1);
        model.setUpdatedAt(LocalDateTime.now());
        model.setUpdatedBy(headerInfo.getUserId());
        model.setUpdatedByName(headerInfo.getUsername());
    }

    private HdsDeviceLogEntity buildDeviceLog(HdsDeviceEntity device,
                                              HdsDeviceModelEntity model, HeaderUtils.HeaderInfo headerInfo) {
        HdsDeviceLogEntity entity = new HdsDeviceLogEntity();
        entity.setDeviceId(device.getDeviceId());
        entity.setDeviceSn(device.getDeviceSn());
        entity.setEventType(DeviceEventTypeEnum.INBOUND.getCode());
        entity.setEventTime(LocalDateTime.now());
        entity.setRemark(String.format("设备[%s]入库，型号[%s]，型号名称[%s]",
                device.getDeviceSn(), model.getModelCode(), model.getModelName()));
        setAuditFields(entity, headerInfo);
        return entity;
    }

    private void setAuditFields(BaseEntity entity, HeaderUtils.HeaderInfo headerInfo) {
        entity.setCreatedBy(headerInfo.getUserId());
        entity.setCreatedByName(headerInfo.getUsername());
        entity.setCreatedAt(LocalDateTime.now());
    }

    @Override
    public Flux<DeviceImportDTO> exportData(Map<String, Object> params) {
        return null;
    }

    @Override
    public String getExportFileName(Map<String, Object> params) {
        return "";
    }

    @Data
    static class ExcelSaveReq {
        private Long modelId;

        private String modelName;

        private String deviceSn;

        private String imei;

        private String warrantyStart;

        private String warrantyEnd;

        private String remark;

    }
}
