package com.wormhole.hotelds.excel.dailyaistatistics;

import com.wormhole.hotelds.admin.model.entity.HdsHotelDailyAiStatisticsEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * @Author: AI Assistant
 * @Date: 2025-08-15
 * @Description: 门店日报数据导出DTO
 */
@Data
@Accessors(chain = true)
public class DailyAiStatisticsExportDTO {

    /**
     * 日期
     */
    private String date;

    /**
     * 使用房间数
     */
    private Integer roomUseCount;

    /**
     * AI语音通话数
     */
    private Integer aiCallCount;

    /**
     * 平均通话时长（秒）
     */
    private Float avgCallDurationSeconds;

    /**
     * 文字对话数
     */
    private Integer textDialogueCount;

    /**
     * 生成工单
     */
    private Integer ticketCount;

    /**
     * AI解决数
     */
    private Integer aiSolveCount;

    /**
     * 人工回拨数
     */
    private Integer returnCallCount;

    /**
     * 处理时长
     */
    private Float avgCompleteDurationSeconds;

    /**
     * 超时工单数
     */
    private Integer overdueCount;

    /**
     * 投诉工单数
     */
    private Integer complaintTicketCount;

    /**
     * 将实体转换为DTO
     */
    public static DailyAiStatisticsExportDTO fromEntity(HdsHotelDailyAiStatisticsEntity entity) {
        if (entity == null) {
            return null;
        }

        DailyAiStatisticsExportDTO dto = new DailyAiStatisticsExportDTO();
        dto.setDate(entity.getBusinessDate() != null ? 
            entity.getBusinessDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) : "");
        dto.setRoomUseCount(entity.getRoomUseCount() != null ? entity.getRoomUseCount() : 0);
        dto.setAiCallCount(entity.getAiCallCount() != null ? entity.getAiCallCount() : 0);
        dto.setAvgCallDurationSeconds(entity.getAvgCallDurationSeconds() != null ? entity.getAvgCallDurationSeconds() : 0.0f);
        dto.setTextDialogueCount(entity.getTextDialogueCount() != null ? entity.getTextDialogueCount() : 0);
        dto.setTicketCount(entity.getTicketCount() != null ? entity.getTicketCount() : 0);
        dto.setAiSolveCount(entity.getAiSolveCount() != null ? entity.getAiSolveCount() : 0);
        dto.setReturnCallCount(entity.getReturnCallCount() != null ? entity.getReturnCallCount() : 0);
        dto.setAvgCompleteDurationSeconds(entity.getAvgCompleteDurationSeconds() != null ? entity.getAvgCompleteDurationSeconds() : 0.0f);
        dto.setOverdueCount(entity.getOverdueCount() != null ? entity.getOverdueCount() : 0);
        dto.setComplaintTicketCount(entity.getComplaintTicketCount() != null ? entity.getComplaintTicketCount() : 0);

        return dto;
    }
}
