package com.wormhole.hotelds.excel;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author：flx
 * @Date：2025/4/10 14:49
 * @Description：TaskTypeEnum
 */
@Getter
@AllArgsConstructor
public enum TaskTypeEnum {

    IMPORT(1, "导入"),
    EXPORT(2, "导出");

    /**
     * 类型编码
     */
    private final Integer code;

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 根据编码获取枚举
     */
    public static TaskTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TaskTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 判断是否为导入任务
     */
    public boolean isImport() {
        return IMPORT.equals(this);
    }

    /**
     * 判断是否为导出任务
     */
    public boolean isExport() {
        return EXPORT.equals(this);
    }
}
