package com.wormhole.hotelds.excel.dailyaistatisticsgroup;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.model.entity.HdsHotelDailyAiStatisticsEntity;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import com.wormhole.hotelds.admin.model.enums.HdsHotelDailyAiStatisticsFieldEnum;
import com.wormhole.hotelds.admin.repository.HdsHotelDailyAiStatisticsRepository;
import com.wormhole.hotelds.admin.repository.HotelRepository;
import com.wormhole.hotelds.core.model.entity.HdsHotelInfoEntity;
import com.wormhole.hotelds.excel.DataExportProcessor;
import com.wormhole.hotelds.excel.ExportContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: AI Assistant
 * @Date: 2025-08-15
 * @Description: 集团日报数据导出处理器
 */
@Slf4j
@Component
public class DailyAiStatisticsGroupDataExportProcessor implements DataExportProcessor<DailyAiStatisticsGroupExportDTO, DailyAiStatisticsGroupExportReq> {

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private HdsHotelDailyAiStatisticsRepository repository;

    @Resource
    private HotelRepository hotelRepository;

    @Resource
    private ObjectMapper objectMapper;

    @Override
    public String getBusinessType() {
        return BussinessTypeEnum.DAILY_AI_STATISTICS_GROUP.getBusinessType();
    }

    @Override
    public List<String> getExcelHeaders() {
        return Arrays.asList(
                "酒店",
                "使用房间数",
                "AI语音通话数",
                "平均通话时长（秒）",
                "文字对话数",
                "生成工单数",
                "AI解决数",
                "人工回拨数",
                "处理时长",
                "超时工单数",
                "投诉工单数"
        );
    }

    @Override
    public Flux<DailyAiStatisticsGroupExportDTO> queryData(DailyAiStatisticsGroupExportReq request, ExportContext context) {
        return parseRequestJson(request)
                .flatMapMany(parsedReq -> {
                    // 构建查询条件
                    Criteria criteria = Criteria.where(HdsHotelDailyAiStatisticsFieldEnum.row_status.name()).is(1);

                    // 添加日期范围条件
                    if (StringUtils.isNotBlank(parsedReq.getStartDate())) {
                        LocalDate startDate = LocalDate.parse(parsedReq.getStartDate());
                        criteria = criteria.and(HdsHotelDailyAiStatisticsFieldEnum.business_date.name()).greaterThanOrEquals(startDate);
                    }
                    if (StringUtils.isNotBlank(parsedReq.getEndDate())) {
                        LocalDate endDate = LocalDate.parse(parsedReq.getEndDate());
                        criteria = criteria.and(HdsHotelDailyAiStatisticsFieldEnum.business_date.name()).lessThanOrEquals(endDate);
                    }

                    // 执行查询
                    Query query = Query.query(criteria)
                            .sort(Sort.by(Sort.Direction.ASC, HdsHotelDailyAiStatisticsFieldEnum.hotel_code.name()));

                    return r2dbcEntityTemplate.select(query, HdsHotelDailyAiStatisticsEntity.class)
                            .collectList()
                            .flatMapMany(statisticsList -> {
                                if (statisticsList.isEmpty()) {
                                    return Flux.empty();
                                }

                                // 按酒店编码分组并聚合数据
                                Map<String, List<HdsHotelDailyAiStatisticsEntity>> groupedByHotel = statisticsList.stream()
                                        .collect(Collectors.groupingBy(HdsHotelDailyAiStatisticsEntity::getHotelCode));

                                // 获取所有酒店编码
                                Set<String> hotelCodes = groupedByHotel.keySet();

                                // 批量查询酒店信息
                                return hotelRepository.findByHotelCodeIn(hotelCodes)
                                        .collectMap(HdsHotelInfoEntity::getHotelCode, HdsHotelInfoEntity::getHotelName)
                                        .flatMapMany(hotelNameMap -> {
                                            List<DailyAiStatisticsGroupExportDTO> result = new ArrayList<>();

                                            for (Map.Entry<String, List<HdsHotelDailyAiStatisticsEntity>> entry : groupedByHotel.entrySet()) {
                                                String hotelCode = entry.getKey();
                                                List<HdsHotelDailyAiStatisticsEntity> hotelStatistics = entry.getValue();

                                                // 聚合该酒店的所有数据
                                                DailyAiStatisticsGroupExportDTO dto = aggregateHotelData(hotelCode, hotelStatistics, hotelNameMap);
                                                result.add(dto);
                                            }

                                            return Flux.fromIterable(result);
                                        });
                            });
                });
    }

    @Override
    public List<String> convertToRow(DailyAiStatisticsGroupExportDTO data) {
        List<String> row = new ArrayList<>();
        row.add(data.getHotelName() != null ? data.getHotelName() : "");
        row.add(data.getRoomUseCount() != null ? data.getRoomUseCount().toString() : "0");
        row.add(data.getAiCallCount() != null ? data.getAiCallCount().toString() : "0");
        row.add(data.getAvgCallDurationSeconds() != null ? String.format("%.2f", data.getAvgCallDurationSeconds()) : "0.00");
        row.add(data.getTextDialogueCount() != null ? data.getTextDialogueCount().toString() : "0");
        row.add(data.getTicketCount() != null ? data.getTicketCount().toString() : "0");
        row.add(data.getAiSolveCount() != null ? data.getAiSolveCount().toString() : "0");
        row.add(data.getReturnCallCount() != null ? data.getReturnCallCount().toString() : "0");
        row.add(data.getAvgCompleteDurationSeconds() != null ? String.format("%.2f", data.getAvgCompleteDurationSeconds()) : "0.00");
        row.add(data.getOverdueCount() != null ? data.getOverdueCount().toString() : "0");
        row.add(data.getComplaintTicketCount() != null ? data.getComplaintTicketCount().toString() : "0");
        return row;
    }

    @Override
    public String getExportFileName(DailyAiStatisticsGroupExportReq request) {
        String dateStr = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss").format(LocalDateTime.now());
        return "集团日报数据_" + dateStr + ".xlsx";
    }

    @Override
    public Class<DailyAiStatisticsGroupExportReq> getRequestClass() {
        return DailyAiStatisticsGroupExportReq.class;
    }

    /**
     * 解析请求JSON参数
     */
    private Mono<DailyAiStatisticsGroupExportReq> parseRequestJson(DailyAiStatisticsGroupExportReq request) {
        return Mono.fromCallable(() -> {
            if (StringUtils.isBlank(request.getRequestJson())) {
                throw new BusinessException(ResultCode.INVALID_PARAMETER, "请求参数不能为空");
            }

            try {
                JsonNode jsonNode = objectMapper.readTree(request.getRequestJson());
                String startDate = jsonNode.has("startDate") ? jsonNode.get("startDate").asText() : null;
                String endDate = jsonNode.has("endDate") ? jsonNode.get("endDate").asText() : null;

                request.setStartDate(startDate);
                request.setEndDate(endDate);

                return request;
            } catch (JsonProcessingException e) {
                log.error("解析请求JSON失败: {}", e.getMessage(), e);
                throw new BusinessException(ResultCode.INVALID_PARAMETER, "请求参数格式错误");
            }
        });
    }

    /**
     * 聚合酒店数据
     */
    private DailyAiStatisticsGroupExportDTO aggregateHotelData(String hotelCode, 
                                                               List<HdsHotelDailyAiStatisticsEntity> statistics,
                                                               Map<String, String> hotelNameMap) {
        DailyAiStatisticsGroupExportDTO dto = new DailyAiStatisticsGroupExportDTO();
        dto.setHotelName(hotelNameMap.getOrDefault(hotelCode, hotelCode));

        // 聚合数据
        int totalRoomUseCount = statistics.stream().mapToInt(s -> s.getRoomUseCount() != null ? s.getRoomUseCount() : 0).sum();
        int totalAiCallCount = statistics.stream().mapToInt(s -> s.getAiCallCount() != null ? s.getAiCallCount() : 0).sum();
        int totalTextDialogueCount = statistics.stream().mapToInt(s -> s.getTextDialogueCount() != null ? s.getTextDialogueCount() : 0).sum();
        int totalTicketCount = statistics.stream().mapToInt(s -> s.getTicketCount() != null ? s.getTicketCount() : 0).sum();
        int totalAiSolveCount = statistics.stream().mapToInt(s -> s.getAiSolveCount() != null ? s.getAiSolveCount() : 0).sum();
        int totalReturnCallCount = statistics.stream().mapToInt(s -> s.getReturnCallCount() != null ? s.getReturnCallCount() : 0).sum();
        int totalOverdueCount = statistics.stream().mapToInt(s -> s.getOverdueCount() != null ? s.getOverdueCount() : 0).sum();
        int totalComplaintTicketCount = statistics.stream().mapToInt(s -> s.getComplaintTicketCount() != null ? s.getComplaintTicketCount() : 0).sum();

        // 计算平均值
        double avgCallDuration = statistics.stream()
                .filter(s -> s.getAvgCallDurationSeconds() != null && s.getAvgCallDurationSeconds() > 0)
                .mapToDouble(s -> s.getAvgCallDurationSeconds())
                .average()
                .orElse(0.0);

        double avgCompleteDuration = statistics.stream()
                .filter(s -> s.getAvgCompleteDurationSeconds() != null && s.getAvgCompleteDurationSeconds() > 0)
                .mapToDouble(s -> s.getAvgCompleteDurationSeconds())
                .average()
                .orElse(0.0);

        dto.setRoomUseCount(totalRoomUseCount);
        dto.setAiCallCount(totalAiCallCount);
        dto.setAvgCallDurationSeconds((float) avgCallDuration);
        dto.setTextDialogueCount(totalTextDialogueCount);
        dto.setTicketCount(totalTicketCount);
        dto.setAiSolveCount(totalAiSolveCount);
        dto.setReturnCallCount(totalReturnCallCount);
        dto.setAvgCompleteDurationSeconds((float) avgCompleteDuration);
        dto.setOverdueCount(totalOverdueCount);
        dto.setComplaintTicketCount(totalComplaintTicketCount);

        return dto;
    }
}
