package com.wormhole.hotelds.admin.service;

import com.google.common.base.Preconditions;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.admin.annotation.OperationLog;
import com.wormhole.hotelds.admin.annotation.RequireHotelAccess;
import com.wormhole.hotelds.admin.aop.LoggingContext;
import com.wormhole.hotelds.admin.model.enums.*;
import com.wormhole.hotelds.admin.model.req.*;
import com.wormhole.hotelds.admin.model.vo.*;
import com.wormhole.hotelds.admin.repository.HotelRepository;
import com.wormhole.hotelds.api.hotel.client.HotelDsApiClient;
import com.wormhole.hotelds.config.CommonProperties;
import com.wormhole.hotelds.constant.RoleEnum;
import com.wormhole.hotelds.core.enums.DeviceStatusEnum;
import com.wormhole.hotelds.core.enums.DeviceTypeEnum;
import com.wormhole.hotelds.core.enums.EmployeeStatusEnum;
import com.wormhole.hotelds.core.model.entity.*;
import com.wormhole.hotelds.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuple3;
import reactor.util.function.Tuples;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author：flx
 * @Date：2025/3/26 10:28
 * @Description：门店服务层
 */
@Slf4j
@Service
public class HotelService {

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private HotelRepository hotelRepository;

    @Resource
    private TransactionalOperator transactionalOperator;

    @Resource
    private CodePoolManager codePoolManager;

    @Resource
    private CommonProperties commonProperties;

    @Resource
    private OperationLogUtil operationLogUtil;

    @Resource
    private DeviceLogService deviceLogService;

    @Resource
    private HdsEmployeeService hdsEmployeeService;

    @Resource
    private HotelDsApiClient hotelDsApiClient;

    @Resource
    private DeviceService deviceService;

    @Resource
    private AgentTimeFlowService agentTimeFlowService;

    @Resource
    private UserLeadService userLeadService;

    @Resource
    private HotelLeadService hotelLeadService;

    @Resource
    private UserHotelLeadMappingService userHotelLeadMappingService;

    @Resource
    private HotelServiceExpiryHandler hotelServiceExpiryHandler;

    @Resource
    private DeviceMonitorHandler deviceMonitorHandler;

    @Resource
    private MerchantService merchantService;

    @Resource
    private BrandService brandService;

    @Resource
    private HdsEmployeeHotelService hdsEmployeeHotelService;

    @Resource
    private OrderService orderService;

    /**
     * 创建门店
     *
     * @param hotelSaveReq:创建门店请求
     * @return：创建结果
     */
    @OperationLog(businessType = BussinessTypeEnum.HOTEL,
            operationType = OperationTypeEnum.ADD)
    public Mono<HotelCreateVO> create(HotelSaveReq hotelSaveReq) {
        ValidatorUtils.validateHotelSaveReq(hotelSaveReq);
        return checkHotelNameIsExist(hotelSaveReq.getHotelName(), null)
                .flatMap(isExist -> {
                    if (isExist && Objects.equals(hotelSaveReq.getSource(), 0)) {
                        return Mono.error(new BusinessException("DATA-REPEAT", String.format("该门店已存在，门店名称：%s", hotelSaveReq.getHotelName())));
                    }
                    return HeaderUtils.getHeaderInfo()
                            .flatMap(headerInfo ->
                                    getHotelCode(hotelSaveReq)
                                            .flatMap(code -> {
                                                if (Objects.equals(hotelSaveReq.getSource(), 0)) {
                                                    return executeTransferWithinTransactionCreateFromWeb(hotelSaveReq, headerInfo, code);
                                                } else {
                                                    return executeTransferWithinTransactionCreateFromOTA(hotelSaveReq, headerInfo, code);
                                                }
                                            }))
                            .flatMap(result -> {
                                HotelCreateVO hotelCreateVO = new HotelCreateVO();
                                hotelCreateVO.setHotelCode(result.getT2());
                                // 如果酒店创建成功且填写了门店联系人、联系电话、邮箱，则创建员工账号
                                if (result.getT1() && StringUtils.isNotBlank(hotelSaveReq.getMainPerson())
                                        && StringUtils.isNotBlank(hotelSaveReq.getPhone())) {
                                    String hotelCode = result.getT2();
                                    return hdsEmployeeService.createHotelEmployee(hotelSaveReq, hotelCode).thenReturn(hotelCreateVO);
                                }
                                return Mono.just(hotelCreateVO);
                            })
                            .onErrorResume(ex -> {
                                if (ex instanceof BusinessException) {
                                    return Mono.error(ex);
                                }
                                return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "hotel create error: " + ex.getMessage()));
                            });
                });
    }

    /**
     * 事务创建
     *
     * @return Tuple2<Boolean, String> - 第一个元素表示是否成功，第二个元素是酒店编码
     */
    private Mono<Tuple2<Boolean, String>> executeTransferWithinTransactionCreateFromWeb(HotelSaveReq req, HeaderUtils.HeaderInfo headerInfo, String code) {
        // 1、判断品牌是否存在
        Mono<HdsBrandEntity> brandMono = brandService.getOrPrepareBrand(req.getBrandName(), headerInfo);

        // 2、判断商户是否存在
        Mono<HdsMerchantEntity> merchantMono = merchantService.getOrPrepareMerchantForCreate(req.getMerchantName(), req.getMerchantShortName(), headerInfo);

        Mono<HdsAgentTimeFlowEntity> agentTimeFlowMono = agentTimeFlowService.getOrPrepareSaveAgentTimeFlow(req, Mono.empty(), headerInfo, true);

        // 4、组合所有前置条件
        return Mono.zip(brandMono, merchantMono, agentTimeFlowMono)
                .flatMap(tuple -> {
                    HdsBrandEntity brand = tuple.getT1();
                    HdsMerchantEntity merchant = tuple.getT2();
                    HdsAgentTimeFlowEntity agentTimeFlow = tuple.getT3();


                    // 5、构建酒店实体
                    HdsHotelInfoEntity hotel = buildHdsHotelInfoEntity(req, headerInfo, brand, merchant, code);
                    hotel.setInitFinishedAt(LocalDateTime.now());

                    // 设置门店code
                    if (agentTimeFlow.getMonthsChanged() != null) {
                        agentTimeFlow.setHotelCode(code);
                        agentTimeFlow.setExpirationBefore(hotel.getInitFinishedAt());
                        agentTimeFlow.setExpirationAfter(hotel.getInitFinishedAt().plusMonths(agentTimeFlow.getMonthsChanged()));
                    }

                    // 6、执行事务操作
                    return executeTransactionCreate(req, hotel, brand, merchant, agentTimeFlow, headerInfo)
                            .map(success -> Tuples.of(success, code));
                });
    }


    /**
     * 事务创建（从OTA创建）
     *
     * @return Tuple2<Boolean, String> - 第一个元素表示是否成功，第二个元素是酒店编码
     */
    private Mono<Tuple2<Boolean, String>> executeTransferWithinTransactionCreateFromOTA(HotelSaveReq req, HeaderUtils.HeaderInfo headerInfo, String code) {
        // 1.生成酒店编码
        Mono<HdsHotelInfoEntity> hotelMono;
        if (StringUtils.isNotBlank(req.getHotelCode())) {
            hotelMono = hotelRepository.findByHotelCodeAndStatus(req.getHotelCode(), 1)
                    .flatMap(hotel -> {
//                        if (Objects.equals(hotel.getStatus(), 1)) {
//                            return Mono.error(new BusinessException("DATA-ALREADY-INIT", "该门店已初始化完成"));
//                        }
                        return Mono.just(hotel);
                    })
                    .switchIfEmpty(Mono.error(new BusinessException("DATA-NOT-EXIST", "门店不存在")));
        } else {
            hotelMono = hotelRepository.findByHotelNameAndStatusIn(req.getHotelName(), Arrays.asList(1, 2))
                    .flatMap(hotel -> {
                        req.setHotelCode(hotel.getHotelCode());
//                        if (Objects.equals(hotel.getStatus(), 1)) {
//                            return Mono.error(new BusinessException("DATA-ALREADY-INIT", "该门店已初始化完成"));
//                        } else {
                        hotel.setCtripEbkUrl(req.getCtripEbkUrl());
                        return Mono.just(hotel);
//                        }
                    }).switchIfEmpty(Mono.just(buildHdsHotelInfoEntity(req, headerInfo, null, null, code)))
                    .flatMap(hotel -> userLeadService.findByInviteeId(Integer.valueOf(headerInfo.getUserId()))
                            .map(userLeadEntity -> {
                                hotel.setInviteCode(userLeadEntity.getInviteCode());
                                return hotel;
                            }).defaultIfEmpty(hotel));
        }

        return hotelMono.flatMap(hotel -> {
            String hotelCode = hotel.getHotelCode();
            req.setHotelCode(hotelCode);

            return hotelLeadService.getOrPrepareHotelLead(req, headerInfo)
                    .flatMap(hotelLead -> userHotelLeadMappingService.getOrPrepareUserHotelLeadMapping(hotelLead, req, headerInfo)
                            .flatMap(mapping -> {
                                hotel.setHotelName(req.getHotelName());
                                hotel.setCtripEbkUrl(req.getCtripEbkUrl());
//                                if (Objects.equals(hotel.getSource(), 1)) {
//                                    hotel.setTotalRoom(req.getTotalRoom());
//                                    hotel.setCountryCode(null);
//                                    hotel.setCountryName(null);
//                                    hotel.setProvinceCode(null);
//                                    hotel.setProvinceName(null);
//                                    hotel.setCityCode(null);
//                                    hotel.setCityName(null);
//                                    hotel.setDistrictCode(null);
//                                    hotel.setDistrictName(null);
//                                    hotel.setAddress(req.getAddress());
//                                    hotel.setStatus(2);
//                                }

                                return hotelRepository.save(hotel)
                                        .flatMap(savedHotel -> {
                                            // 保存酒店线索
                                            return hotelLeadService.save(hotelLead);
                                        })
                                        .flatMap(savedHotelLead -> {
                                            // 设置外键关系并保存映射实体
                                            if (Objects.isNull(mapping.getId())) {
                                                mapping.setHotelLeadId(savedHotelLead.getId());
                                                return userHotelLeadMappingService.save(mapping)
                                                        .thenReturn(savedHotelLead);
                                            }
                                            return Mono.just(savedHotelLead);
                                        })
                                        .map(savedHotelLead -> {
                                            // 记录操作日志
                                            log.info("门店创建成功: hotelCode={}, name={}",
                                                    hotelCode, hotel.getHotelName());
                                            return Tuples.of(Boolean.TRUE, hotelCode);
                                        });
                            }));
        }).as(transactionalOperator::transactional);
    }

    /**
     * 获取门店编码
     * 规则：
     * 1. source=0(web创建)时，总是生成新编码
     * 2. source=1(OTA创建)且hotelCode不为空时，使用传入的hotelCode
     * 3. source=1(OTA创建)且hotelCode为空时，生成新编码
     *
     * @param req 请求参数
     * @return 门店编码Mono
     */
    private Mono<String> getHotelCode(HotelSaveReq req) {
        // 如果是OTA创建且已有hotelCode，直接使用
        if (Objects.equals(req.getSource(), 1) && StringUtils.isNotBlank(req.getHotelCode())) {
            return Mono.just(req.getHotelCode());
        }

        // 否则从池中获取新编码
        return codePoolManager.getCodeFromPool(BussinessTypeEnum.HOTEL.getBusinessType());
    }

    private Mono<Boolean> checkHotelNameIsExist(String hotelName, HotelSaveReq hotelSaveReq) {
        if (StringUtils.isBlank(hotelName)) {
            return Mono.just(false);
        }
        // 如果hotelSaveReq为null（新增场景），直接检查名称是否存在
        if (hotelSaveReq == null) {
            return hotelRepository.existsByHotelNameAndStatus(hotelName, 1);
        }
        // 如果id不为null（修改场景），需要排除当前酒店ID
        else {
            if (Objects.nonNull(hotelSaveReq.getId())) {
                return hotelRepository.existsByHotelNameAndIdNotAndStatus(hotelName, hotelSaveReq.getId(), 1);
            } else if (StringUtils.isNotBlank(hotelSaveReq.getHotelCode())) {
                return hotelRepository.existsByHotelNameAndHotelCodeNotAndStatus(hotelName, hotelSaveReq.getHotelCode(), 1);
            } else {
                return Mono.just(false);
            }
        }
    }

    /**
     * 执行事务操作
     *
     * @param hotel    门店实体
     * @param brand    品牌实体
     * @param merchant 商户实体
     * @return 返回true表示成功，false表示失败
     */
    private Mono<Boolean> executeTransactionCreate(HotelSaveReq req,
                                                   HdsHotelInfoEntity hotel,
                                                   HdsBrandEntity brand,
                                                   HdsMerchantEntity merchant,
                                                   HdsAgentTimeFlowEntity agentTimeFlowEntity,
                                                   HeaderUtils.HeaderInfo headerInfo) {
        // 创建事务操作
        Mono<Tuple3<HdsHotelInfoEntity, HdsBrandEntity, HdsMerchantEntity>> operations = Mono.defer(() -> {
            List<Mono<?>> saveOperations = new ArrayList<>();

            // 1、处理品牌保存（根据ID判断是否需要保存）
            if (brand.getId() == null && StringUtils.isNotBlank(brand.getBrandName())) {
                saveOperations.add(brandService.save(brand)
                        .doOnSuccess(savedBrand -> {
                            // 更新ID以便后续使用
                            brand.setId(savedBrand.getId());
                        }));
            }

            // 2、处理商户保存（根据ID判断是否需要保存）
            if (merchant.getId() == null && (
                    StringUtils.isNotBlank(merchant.getMerchantName()) ||
                            StringUtils.isNotBlank(merchant.getSubjectName()))) {
                saveOperations.add(merchantService.save(merchant)
                        .doOnSuccess(savedMerchant -> {
                            // 更新ID以便后续使用
                            merchant.setId(savedMerchant.getId());
                        }));
            }

            // 3、执行所有保存操作，然后保存酒店
            if (saveOperations.isEmpty()) {
                // 没有前置保存，直接保存酒店
                return hotelRepository.save(hotel)
                        .map(savedHotel -> Tuples.of(savedHotel, brand, merchant));
            } else {
                // 先执行前置保存，再保存酒店
                return Flux.concat(saveOperations)
                        .then(hotelRepository.save(hotel))
                        .map(savedHotel -> Tuples.of(savedHotel, brand, merchant));
            }
        });
        // 使用事务包装并执行
        return executeTransactional(req, operations, agentTimeFlowEntity, true);
    }

    /**
     * 更新酒店
     */
    @OperationLog(businessType = BussinessTypeEnum.HOTEL,
            operationType = OperationTypeEnum.UPDATE)
    public Mono<Boolean> update(HotelSaveReq hotelSaveReq) {
        Preconditions.checkArgument(Objects.nonNull(hotelSaveReq), "hotelSaveReq must not be null");
        Preconditions.checkArgument(Objects.nonNull(hotelSaveReq.getId()) || StringUtils.isNotBlank(hotelSaveReq.getHotelCode()), "id must not be null");
        return checkHotelNameIsExist(hotelSaveReq.getHotelName(), hotelSaveReq)
                .flatMap(isExist -> {
                    if (isExist) {
                        return Mono.error(new BusinessException("DATA-REPEAT", String.format("该门店已存在，门店名称：%s", hotelSaveReq.getHotelName())));
                    }
                    return HeaderUtils.getHeaderInfo()
                            .flatMap(headerInfo -> executeTransferWithinTransactionUpdate(hotelSaveReq, headerInfo)
                                    .onErrorResume(ex -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "hotel update error")))
                            );
                });
    }

    /**
     * 事务更新
     */
    private Mono<Boolean> executeTransferWithinTransactionUpdate(HotelSaveReq req, HeaderUtils.HeaderInfo headerInfo) {
        // 1. 查询现有酒店
        Mono<HdsHotelInfoEntity> existingHotelMono;

        // 优先使用ID查询，如果ID为空则使用hotelCode查询
        if (Objects.nonNull(req.getId())) {
            existingHotelMono = hotelRepository.findById(req.getId())
                    .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "酒店ID不存在: " + req.getId())));
        } else if (StringUtils.isNotBlank(req.getHotelCode())) {
            existingHotelMono = hotelRepository.findByHotelCode(req.getHotelCode())
                    .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "酒店编码不存在: " + req.getHotelCode())));
        } else {
            // 如果ID和hotelCode都为空，则返回错误
            existingHotelMono = Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "酒店ID和酒店编码不能同时为空"));
        }

        // 2. 判断品牌是否存在
        Mono<HdsBrandEntity> brandMono = brandService.getAndUpdateBrand(req.getBrandName(), headerInfo, existingHotelMono);

        // 3. 判断商户是否存在
        Mono<HdsMerchantEntity> merchantMono = merchantService.getAndUpdateMerchant(req.getMerchantName(), req.getMerchantShortName(), headerInfo, existingHotelMono);

        Mono<HdsAgentTimeFlowEntity> agentTimeFlowMono = agentTimeFlowService.getOrPrepareSaveAgentTimeFlow(req, existingHotelMono, headerInfo, false);

        // 4. 组合所有前置条件
        return Mono.zip(existingHotelMono, brandMono, merchantMono, agentTimeFlowMono)
                .flatMap(tuple -> {
                    HdsHotelInfoEntity existingHotel = tuple.getT1();
                    HdsBrandEntity brand = tuple.getT2();
                    HdsMerchantEntity merchant = tuple.getT3();
                    HdsAgentTimeFlowEntity agentTimeFlow = tuple.getT4();

                    // 5. 更新酒店实体
                    updateHdsHotelInfoEntity(existingHotel, brand, merchant, req, headerInfo);

                    // 6. 执行事务操作
                    return executeTransactionUpdate(req, existingHotel, brand, merchant, agentTimeFlow);
                });
    }


    /**
     * 执行事务操作
     *
     * @param hotel    门店实体
     * @param brand    品牌实体
     * @param merchant 商户实体
     * @return 返回true表示成功，false表示失败
     */
    private Mono<Boolean> executeTransactionUpdate(HotelSaveReq req,
                                                   HdsHotelInfoEntity hotel,
                                                   HdsBrandEntity brand,
                                                   HdsMerchantEntity merchant,
                                                   HdsAgentTimeFlowEntity agentTimeFlow) {
        // 创建事务操作
        Mono<Tuple3<HdsHotelInfoEntity, HdsBrandEntity, HdsMerchantEntity>> operations = Mono.defer(() -> {
            List<Mono<?>> saveOperations = new ArrayList<>();

            // 1、处理品牌保存
            if (StringUtils.isNotBlank(brand.getBrandName())) {
                saveOperations.add(brandService.save(brand)
                        .doOnSuccess(savedBrand -> {
                            brand.setId(savedBrand.getId());
                        }));
            }

            // 2、处理商户保存
            if (StringUtils.isNotBlank(merchant.getMerchantName()) || StringUtils.isNotBlank(merchant.getSubjectName())) {
                saveOperations.add(merchantService.save(merchant)
                        .doOnSuccess(savedMerchant -> {
                            merchant.setId(savedMerchant.getId());
                        }));
            }

            // 3、执行所有保存操作，然后保存酒店
            if (saveOperations.isEmpty()) {
                // 没有前置保存，直接保存酒店
                return hotelRepository.save(hotel)
                        .map(savedHotel -> Tuples.of(savedHotel, brand, merchant));
            } else {
                // 先执行前置保存，再保存酒店
                return Flux.concat(saveOperations)
                        .then(hotelRepository.save(hotel))
                        .map(savedHotel -> Tuples.of(savedHotel, brand, merchant));
            }
        });
        // 使用事务包装并执行
        return executeTransactional(req, operations, agentTimeFlow, false);
    }

    @NotNull
    private Mono<Boolean> executeTransactional(HotelSaveReq req,
                                               Mono<Tuple3<HdsHotelInfoEntity, HdsBrandEntity, HdsMerchantEntity>> operations,
                                               HdsAgentTimeFlowEntity agentTimeFlow,
                                               boolean isCreate) {
        String operationType = isCreate ? "创建" : "更新";
        return transactionalOperator.transactional(operations)
                .flatMap(tuple -> {
                    HdsHotelInfoEntity savedHotel = tuple.getT1();
                    HdsBrandEntity savedBrand = tuple.getT2();
                    HdsMerchantEntity savedMerchant = tuple.getT3();

                    req.setId(savedHotel.getId());

                    log.info("酒店{}成功: id={}, code={}, name={}, brandId={}, brandCode={}, merchantId={}",
                            operationType,
                            savedHotel.getId(), savedHotel.getHotelCode(), savedHotel.getHotelName(),
                            savedBrand.getId(), savedBrand.getBrandCode(),
                            savedMerchant.getId());

                    // 有条件地保存流水记录，但使用更安全的方式
                    if (Objects.nonNull(agentTimeFlow) && StringUtils.isNotBlank(agentTimeFlow.getHotelCode())) {
                        return agentTimeFlowService.save(agentTimeFlow)
                                .doOnSuccess(savedFlow ->
                                        log.info("流水记录保存成功: hotelCode={}, flowType={}",
                                                savedFlow.getHotelCode(), savedFlow.getFlowType()))
                                .doOnError(e ->
                                        log.error("流水记录保存失败: {}", e.getMessage(), e))
                                .thenReturn(true)
                                // 即使流水记录保存失败，也认为主操作成功
                                .onErrorReturn(true);
                    }
                    return Mono.just(true);
                })
                .onErrorResume(e -> {
                    log.error("{}酒店失败: {}", operationType, e.getMessage(), e);
                    if (e instanceof DataIntegrityViolationException) {
                        log.error("数据完整性错误，可能是重复数据: {}", e.getMessage());
                    }
                    return Mono.error(e);
                });
    }

    /**
     * 删除酒店
     */
    @OperationLog(businessType = BussinessTypeEnum.HOTEL,
            operationType = OperationTypeEnum.DELETE)
    public Mono<Boolean> delete(HotelDeleteReq req) {
        Preconditions.checkArgument(Objects.nonNull(req), "req must not be null");
        Preconditions.checkArgument(Objects.nonNull(req.getId()), "id must not be null");

        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> executeTransferWithinTransactionDelete(headerInfo, req)
                        .onErrorResume(ex -> {
                            if (ex instanceof BusinessException) {
                                return Mono.error(ex);
                            }
                            return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR,
                                    "hotel delete error: " + ex.getMessage()));
                        })
                );
    }


    /**
     * 事务删除
     */
    private Mono<Boolean> executeTransferWithinTransactionDelete(HeaderUtils.HeaderInfo headerInfo, HotelDeleteReq req) {
        // 1. 查询酒店信息
        return hotelRepository.findById(req.getId())
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "门店不存在")))
                .flatMap(hotel -> {
                    if (Objects.equals(hotel.getStatus(), 1)) {
                        return Mono.error(new BusinessException(ResultCode.NOT_ACCEPTABLE, "门店非停用状态,不能删除"));
                    }
                    return logicalDeleteHotelWithRelations(hotel, headerInfo);
                });
    }

    /**
     * 检查关联设备并逻辑删除酒店及关联资源
     */
    private Mono<Boolean> logicalDeleteHotelWithRelations(HdsHotelInfoEntity hotel, HeaderUtils.HeaderInfo headerInfo) {
        String hotelCode = hotel.getHotelCode();
        Instant startTime = Instant.now();

        // 1. 查询关联的员工
        Mono<List<HdsEmployeeHotelEntity>> employeesMono = hdsEmployeeHotelService.findByHotelCode(hotelCode)
                .collectList()
                .defaultIfEmpty(Collections.emptyList());

        // 2. 查询关联的设备
        Mono<List<HdsDeviceEntity>> devicesMono = deviceService.findByHotelCode(hotelCode)
                .collectList()
                .defaultIfEmpty(Collections.emptyList());

        // 3. 并行获取员工和设备数据
        return Mono.zip(employeesMono, devicesMono)
                .flatMap(tuple -> {
                    List<HdsEmployeeHotelEntity> employees = tuple.getT1();
                    List<HdsDeviceEntity> devices = tuple.getT2();

                    log.info("酒店[{}]逻辑删除前检查: 关联员工 {} 个, 关联设备 {} 个",
                            hotel.getHotelName(), employees.size(), devices.size());

                    // 4. 逻辑删除酒店 - 设置row_status=0
                    hotel.setRowStatus(0);
                    hotel.setUpdatedAt(LocalDateTime.now());
                    hotel.setUpdatedBy(headerInfo.getUserId());
                    hotel.setUpdatedByName(headerInfo.getUsername());

                    // 5. 准备员工逻辑删除 - 所有关联员工row_status=0
                    List<HdsEmployeeHotelEntity> updatedEmployees = employees.isEmpty() ? Collections.emptyList() :
                            employees.stream()
                                    .peek(employee -> {
                                        employee.setRowStatus(0);
                                        employee.setUpdatedAt(LocalDateTime.now());
                                        employee.setUpdatedBy(headerInfo.getUserId());
                                        employee.setUpdatedByName(headerInfo.getUsername());
                                    })
                                    .collect(Collectors.toList());

                    // 6. 准备设备更新 - 参考executeTransferWithinTransactionUnbind方法
                    List<HdsDeviceEntity> validDevices = devices.isEmpty() ? Collections.emptyList() :
                            devices.stream()
                                    .filter(d -> DeviceStatusEnum.PENDING_ACTIVATION.getCode() != d.getDeviceStatus())
                                    .toList();

                    List<HdsDeviceEntity> beforeDevices = validDevices.isEmpty() ? Collections.emptyList() :
                            validDevices.stream()
                                    .map(device -> {
                                        HdsDeviceEntity copy = new HdsDeviceEntity();
                                        BeanUtils.copyProperties(device, copy);
                                        return copy;
                                    })
                                    .toList();

                    List<HdsDeviceEntity> updatedDevices = validDevices.isEmpty() ? Collections.emptyList() :
                            validDevices.stream()
                                    .peek(device -> {
                                        device.setDeviceStatus(DeviceStatusEnum.PENDING_ACTIVATION.getCode());
                                        device.setHotelCode(null);
                                        device.setPositionCode(null);
                                        device.setActiveTime(null);
                                        device.setRtcUserId(null);
                                        device.setUserId(null);
                                        device.setUserName(null);
                                        device.setUpdatedBy(headerInfo.getUserId());
                                        device.setUpdatedByName(headerInfo.getUsername());
                                        device.setUpdatedAt(LocalDateTime.now());
                                    })
                                    .collect(Collectors.toList());

                    // 创建事务操作
                    Mono<Tuple3<HdsHotelInfoEntity, List<HdsEmployeeHotelEntity>, List<HdsDeviceEntity>>> transactionalOperations = Mono.defer(() -> {
                        // 保存酒店信息
                        Mono<HdsHotelInfoEntity> hotelInfoEntityMono = hotelRepository.save(hotel);

                        // 保存员工信息
                        Mono<List<HdsEmployeeHotelEntity>> updateEmployeesMono = hdsEmployeeHotelService.saveAll(updatedEmployees);

                        // 保存设备信息
                        Mono<List<HdsDeviceEntity>> updateDevicesMono = deviceService.saveAll(updatedDevices);

                        // 在事务中组合所有数据库操作
                        return Mono.zip(hotelInfoEntityMono, updateEmployeesMono, updateDevicesMono);
                    });

                    // 执行事务操作，然后执行后续非事务操作
                    return transactionalOperator.transactional(transactionalOperations)
                            .flatMap(transactionResult -> {
                                HdsHotelInfoEntity savedHotel = transactionResult.getT1();
                                List<HdsEmployeeHotelEntity> savedEmployees = transactionResult.getT2();
                                List<HdsDeviceEntity> afterDevices = transactionResult.getT3();

                                log.info("酒店[{}]逻辑删除事务完成: 酒店已设置为无效, 员工{}个, 设备{}个",
                                        hotel.getHotelName(), savedEmployees.size(), afterDevices.size());

                                // 事务成功后，执行设备登出操作
                                if (validDevices.isEmpty()) {
                                    // 没有设备需要处理，直接返回成功
                                    return Mono.just(Boolean.TRUE);
                                }

                                // 处理设备登出
                                List<Mono<Boolean>> logoutMonos = validDevices.stream()
                                        .map(device -> hotelDsApiClient.deviceLogOut(device.getDeviceId())
                                                .defaultIfEmpty(Boolean.FALSE)
                                                .doOnNext(result -> {
                                                    log.info("device[{}]logout result: {}", device.getId(), result);
                                                    if (!result) {
                                                        log.warn("设备[{}]登出失败，但将继续处理其他设备", device.getId());
                                                    }
                                                })
                                                .onErrorResume(e -> {
                                                    log.error("设备[{}]登出异常: {}", device.getId(), e.getMessage(), e);
                                                    return Mono.just(Boolean.FALSE);
                                                }))
                                        .toList();

                                return Flux.concat(logoutMonos)
                                        .collectList()
                                        .flatMap(logoutResults -> {
                                            log.info("设备登出结果: 总数 {}, 成功 {}",
                                                    logoutResults.size(),
                                                    logoutResults.stream().filter(Boolean::booleanValue).count());

                                            // 设备登出后，记录操作日志和发送RTC消息
                                            // 只有在有设备需要处理时才记录操作日志和发送RTC消息
                                            if (!afterDevices.isEmpty()) {
                                                // 记录操作日志
                                                DeviceUnbindReq deviceUnbindReq = new DeviceUnbindReq();
                                                deviceUnbindReq.setDeviceIds(validDevices.stream().map(HdsDeviceEntity::getId).toList());

                                                deviceService.handleBindSuccess(startTime, beforeDevices, afterDevices, deviceUnbindReq, headerInfo);

                                                // 发送RTC消息 -> 发送设备离线消息
                                                List<String> deviceIds = validDevices.stream().map(HdsDeviceEntity::getDeviceId).collect(Collectors.toList());
                                                return deviceService.sendDeviceRtcMessage(deviceIds, "false")
                                                        .then(deviceMonitorHandler.sendDeviceOfflineMessage(hotelCode, ChannelEventEnum.device_offline))
                                                        .thenReturn(Boolean.TRUE);
                                            }

                                            return Mono.just(Boolean.TRUE);
                                        });
                            });
                })
                .flatMap(result -> {
                    if (result) {
                        // 异步发送酒店不可用消息
                        return hotelServiceExpiryHandler.sendHotelServiceExpiryMessage(hotel.getHotelCode(), ChannelEventEnum.hotel_unavailable)
                                .doOnError(e -> log.error("发送酒店服务到期消息失败: hotelCode={}, error={}", hotel.getHotelCode(), e.getMessage()))
                                .thenReturn(Boolean.TRUE);
                    }
                    return Mono.just(Boolean.FALSE);
                })
                .onErrorResume(error -> {
                    log.error("酒店[{}]逻辑删除失败: {}", hotel.getHotelName(), error.getMessage(), error);
                    return Mono.just(Boolean.FALSE);  // 返回失败但不中断流程
                });
    }


    /**
     * 搜索门店数量
     */
    public Mono<Long> searchCount(HotelSearchReq req, HeaderUtils.HeaderInfo headerInfo) {
        return preFilterAndBuildCriteria(req, headerInfo)
                .flatMap(criteria -> {
                    // 执行计数查询
                    return r2dbcEntityTemplate.count(Query.query(criteria), HdsHotelInfoEntity.class);
                })
                .defaultIfEmpty(0L)
                .doOnSuccess(count -> log.info("门店计数成功: count={}, req={}", count, req))
                .onErrorResume(e -> {
                    log.error("门店计数失败: req={}, error={}", req, e.getMessage(), e);
                    return Mono.just(0L);
                });
    }

    /**
     * 搜索门店列表
     */
    public Mono<List<HotelSearchVO>> search(HotelSearchReq req, HeaderUtils.HeaderInfo headerInfo) {
        // 预过滤并构建查询条件
        return preFilterAndBuildCriteria(req, headerInfo)
                .flatMap(criteria -> {
                    // 添加排序和分页
                    Query query = Query.query(criteria)
                            .sort(Sort.by(Sort.Direction.DESC, HdsHotelInfoFieldEnum.created_at.name()))
                            .limit(req.getPageSize())
                            .offset((long) (req.getCurrent() - 1) * req.getPageSize());

                    // 执行查询
                    return r2dbcEntityTemplate.select(HdsHotelInfoEntity.class)
                            .matching(query)
                            .all()
                            .collectList()
                            .flatMap(this::enrichHotelsWithRelatedData);
                })
                .doOnSuccess(list -> log.info("门店查询成功: size={}, req={}", list.size(), req))
                .onErrorResume(e -> {
                    log.error("门店查询失败: req={}, error={}", req, e.getMessage(), e);
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "门店查询失败"));
                });
    }

    /**
     * 根据查询条件预先过滤相关数据，构建更精确的查询条件
     */
    private Mono<Criteria> preFilterAndBuildCriteria(HotelSearchReq hotelSearchReq, HeaderUtils.HeaderInfo headerInfo) {
        // 基础条件
        Criteria baseCriteria = buildBaseCriteria(hotelSearchReq);

        // 排除未初始化完成的门店
        baseCriteria = baseCriteria.and(HdsHotelInfoFieldEnum.status.name()).not(2)
                .and(HdsHotelInfoFieldEnum.row_status.name()).is(1);

        Mono<Criteria> merchantNameMono = merchantService.preFilterByMerchantName(hotelSearchReq, baseCriteria);

        // 判断是否需要过滤员工ID
        Criteria finalBaseCriteria = baseCriteria;
        return hdsEmployeeService.needFilterByEmployeeId(headerInfo)
                .flatMap(needFilter -> {
                    if (Boolean.TRUE.equals(needFilter)) {
                        // 需要过滤员工ID
                        Mono<Criteria> employeeIdMono = hdsEmployeeHotelService.preFilterByEmployeeId(headerInfo, finalBaseCriteria,false);

                        // 合并两个过滤条件
                        return Mono.zip(merchantNameMono, employeeIdMono)
                                .map(tuple -> {
                                    Criteria merchantCriteria = tuple.getT1();
                                    Criteria employeeIdCriteria = tuple.getT2();

                                    // 如果商户条件是基础条件，直接返回员工ID条件
                                    if (merchantCriteria == finalBaseCriteria) {
                                        return employeeIdCriteria;
                                    }

                                    // 否则，合并两个条件
                                    return merchantCriteria.and(employeeIdCriteria);
                                });
                    } else {
                        // 不需要过滤员工ID，直接返回商户名称过滤结果
                        return merchantNameMono;
                    }
                });
    }

    /**
     * 批量查询酒店的关联数据
     *
     * @param hotels
     * @return
     */
    private Mono<List<HotelSearchVO>> enrichHotelsWithRelatedData(List<HdsHotelInfoEntity> hotels) {
        if (hotels.isEmpty()) {
            return Mono.just(Collections.emptyList());
        }

        // 1. 提取所需的关联ID和编码
        Set<String> merchantIds = hotels.stream()
                .map(HdsHotelInfoEntity::getMerchantId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

        Set<String> hotelCodes = hotels.stream()
                .map(HdsHotelInfoEntity::getHotelCode)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

        Set<Integer> createdBySet = hotels.stream()
                .map(HdsHotelInfoEntity::getCreatedBy)
                .filter(s -> StringUtils.isNotBlank(s) && !StringUtils.equals("系统自动", s))
                .filter(s -> s.matches("\\d+"))
                .map(Integer::valueOf)
                .collect(Collectors.toSet());

        // 2. 批量查询关联数据
        Mono<Map<String, HdsMerchantEntity>> merchantMapMono = merchantIds.isEmpty()
                ? Mono.just(Collections.emptyMap())
                : r2dbcEntityTemplate.select(HdsMerchantEntity.class)
                .matching(Query.query(Criteria.where(HdsMerchantFieldEnum.merchant_id.name()).in(merchantIds)))
                .all()
                .collectMap(HdsMerchantEntity::getMerchantId);

        Mono<Map<String, Long>> deviceCountMapMono = hotelCodes.isEmpty()
                ? Mono.just(Collections.emptyMap())
                : r2dbcEntityTemplate.select(HdsDeviceEntity.class)
                .matching(Query.query(Criteria.where(HdsDeviceFieldEnum.hotel_code.name()).in(hotelCodes)
                        .and(HdsDeviceFieldEnum.device_app_type.name()).not(DeviceTypeEnum.FRONT_APP.getCode())))
                .all()
                .groupBy(HdsDeviceEntity::getHotelCode)
                .flatMap(group -> Mono.zip(
                        Mono.just(group.key()),
                        group.count()
                ))
                .collectMap(Tuple2::getT1, Tuple2::getT2);

        Mono<Map<String, HdsEmployeeEntity>> employeeMapMono = createdBySet.isEmpty() ? Mono.just(Collections.emptyMap())
                : r2dbcEntityTemplate.select(HdsEmployeeEntity.class)
                .matching(Query.query(Criteria.where(HdsEmployeeFieldEnum.id.name()).in(createdBySet)))
                .all()
                .collectMap(hdsEmployeeEntity -> String.valueOf(hdsEmployeeEntity.getId()));

        Mono<Map<String, HdsHotelLeadEntity>> hotelLeadMapMono = hotelCodes.isEmpty()
                ? Mono.just(Collections.emptyMap())
                : r2dbcEntityTemplate.select(HdsHotelLeadEntity.class)
                .matching(Query.query(Criteria.where(HdsHotelLeadFieldEnum.hotel_code.name()).in(hotelCodes)))
                .all()
                .collectMap(HdsHotelLeadEntity::getHotelCode);

        // 定义用于存储用户邀请关系的Mono
        Mono<Map<Integer, HdsUserLeadEntity>> userLeadMapMono = hotelLeadMapMono.flatMap(hotelLeadMap -> {
            Set<Integer> adminUserIds = hotelLeadMap.values().stream()
                    .map(HdsHotelLeadEntity::getAdminUserId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            if (adminUserIds.isEmpty()) {
                return Mono.just(Collections.emptyMap());
            }

            return r2dbcEntityTemplate.select(HdsUserLeadEntity.class)
                    .matching(Query.query(Criteria.where(HdsUserLeadFieldEnum.invitee_id.name()).in(adminUserIds)))
                    .all()
                    .collectMap(HdsUserLeadEntity::getInviteeId);
        });

        Mono<Map<Integer, HdsEmployeeEntity>> inviterEmployeeMapMono = Mono.zip(hotelLeadMapMono, userLeadMapMono)
                .flatMap(tuple -> {
                    Map<String, HdsHotelLeadEntity> hotelLeadMap = tuple.getT1();
                    Map<Integer, HdsUserLeadEntity> userLeadMap = tuple.getT2();

                    Set<Integer> inviterIds = userLeadMap.values().stream()
                            .map(HdsUserLeadEntity::getInviterId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());

                    if (inviterIds.isEmpty()) {
                        return Mono.just(Collections.emptyMap());
                    }

                    // 查询邀请人的员工信息
                    return r2dbcEntityTemplate.select(HdsEmployeeEntity.class)
                            .matching(Query.query(Criteria.where(HdsEmployeeFieldEnum.id.name()).in(inviterIds)))
                            .all()
                            .collectMap(HdsEmployeeEntity::getId);
                });

        // 3. 并行执行所有批量查询，并组合结果
        return Mono.zip(merchantMapMono, deviceCountMapMono, employeeMapMono, hotelLeadMapMono, userLeadMapMono, inviterEmployeeMapMono)
                .map(tuple -> {
                    Map<String, HdsMerchantEntity> merchantMap = tuple.getT1();
                    Map<String, Long> deviceCountMap = tuple.getT2();
                    Map<String, HdsEmployeeEntity> employeeMap = tuple.getT3();
                    Map<String, HdsHotelLeadEntity> hotelLeadMap = tuple.getT4();
                    Map<Integer, HdsUserLeadEntity> userLeadMap = tuple.getT5();
                    Map<Integer, HdsEmployeeEntity> inviterEmployeeMap = tuple.getT6();

                    // 4. 将查询结果转换为最终的VO对象
                    return hotels.stream()
                            .map(hotel -> {
                                String merchantId = hotel.getMerchantId();
                                String hotelCode = hotel.getHotelCode();
                                String createdBy = hotel.getCreatedBy();

                                HdsMerchantEntity merchant = merchantMap.getOrDefault(merchantId, new HdsMerchantEntity());
                                Long deviceCount = deviceCountMap.getOrDefault(hotelCode, 0L);
                                HdsEmployeeEntity hdsEmployeeEntity = employeeMap.get(createdBy);
                                HdsHotelLeadEntity hdsHotelLeadEntity = hotelLeadMap.get(hotelCode);

                                // 获取被邀请人信息
                                HdsUserLeadEntity hdsUserLeadEntity = null;
                                HdsEmployeeEntity inviterEmployeeEntity = null;

                                if (hdsHotelLeadEntity != null && hdsHotelLeadEntity.getAdminUserId() != null) {
                                    hdsUserLeadEntity = userLeadMap.get(hdsHotelLeadEntity.getAdminUserId());

                                    // 如果存在邀请关系，获取邀请人员工信息
                                    if (hdsUserLeadEntity != null && hdsUserLeadEntity.getInviterId() != null) {
                                        inviterEmployeeEntity = inviterEmployeeMap.get(hdsUserLeadEntity.getInviterId());
                                    }
                                }

                                return convertToSearchVO(hotel, merchant, deviceCount, hdsEmployeeEntity, hdsHotelLeadEntity, hdsUserLeadEntity, inviterEmployeeEntity);
                            })
                            .collect(Collectors.toList());
                });
    }

    private HotelSearchVO convertToSearchVO(HdsHotelInfoEntity hotel,
                                            HdsMerchantEntity merchant,
                                            Long deviceCount,
                                            HdsEmployeeEntity hdsEmployeeEntity,
                                            HdsHotelLeadEntity hdsHotelLeadEntity,
                                            HdsUserLeadEntity hdsUserLeadEntity,
                                            HdsEmployeeEntity inviterEmployeeEntity) {
        HotelSearchVO hotelSearchVO = new HotelSearchVO();
        BeanUtils.copyProperties(hotel, hotelSearchVO);
        hotelSearchVO.setMerchantShortName(merchant.getSubjectName());
        hotelSearchVO.setRoomNum(hotel.getTotalRoom());
        hotelSearchVO.setDeviceNum(deviceCount.intValue());

        if (Objects.isNull(hdsEmployeeEntity)) {
            hotelSearchVO.setCreatedBy(hotel.getCreatedByName());
        } else {
            hotelSearchVO.setCreatedBy(hdsEmployeeService.generateCreatedByInfo(hdsEmployeeEntity));
        }
        if (Objects.equals(hotel.getSource(), 1)) {
            hotelSearchVO.setCreatedBy("系统自动");
        }

        if (Objects.equals(hotel.getSource(), 0)) {
            hotelSearchVO.setCreateTime(SimpleDateUtils.formatLocalDateTimeToDateHour(hotel.getCreatedAt()));
        }
        if (Objects.equals(hotel.getSource(), 1)) {
            if (hdsHotelLeadEntity != null && hdsHotelLeadEntity.getCompleteTime() != null) {
                hotelSearchVO.setCreateTime(SimpleDateUtils.formatLocalDateTimeToDateHour(hdsHotelLeadEntity.getCompleteTime()));
            }
        }

        if (StringUtils.isNotBlank(hotel.getAiProductTypes())) {
            hotelSearchVO.setAiProductTypes(Arrays.stream(hotel.getAiProductTypes().split(",")).toList());
        }

        if (Objects.nonNull(hdsUserLeadEntity) && Objects.equals(hotel.getSource(), 1)) {
            hotelSearchVO.setInviteCode(hdsUserLeadEntity.getInviteCode());

            // 设置邀请人信息
            Integer inviterId = hdsUserLeadEntity.getInviterId();
            if (inviterId != null) {
                if (inviterEmployeeEntity != null) {
                    // 使用邀请人的员工信息生成完整的邀请人信息
                    hotelSearchVO.setInvitedBy(hdsEmployeeService.generateCreatedByInfo(inviterEmployeeEntity));
                } else {
                    hotelSearchVO.setInvitedBy(String.valueOf(inviterId));
                }
            }
        } else {
            hotelSearchVO.setInviteCode(null);
        }

        // 计算ota过期时间
        LocalDateTime initFinishedAt = hotel.getInitFinishedAt();
        if (CollectionUtils.isNotEmpty(hotelSearchVO.getAiProductTypes()) && hotelSearchVO.getAiProductTypes().contains(AiProductTypeEnum.OTA_AGENT.getCode())) {
            if (Objects.nonNull(hotel.getOtaExtendMonths())) {
                initFinishedAt = initFinishedAt.plusMonths(hotel.getOtaExtendMonths());
            }
            if (Objects.nonNull(hotel.getOtaRewardMonths())) {
                initFinishedAt = initFinishedAt.plusMonths(hotel.getOtaRewardMonths());
            }
            hotelSearchVO.setOtaEndTime(SimpleDateUtils.formatLocalDateTimeToDate(initFinishedAt));
        }
        hotelSearchVO.setSource(hotel.getSource());
        return hotelSearchVO;
    }

    /**
     * 获取门店列表
     *
     * @return
     */
    public Mono<List<HotelVO>> queryList() {
        return r2dbcEntityTemplate.select(HdsHotelInfoEntity.class)
                .matching(Query.empty().limit(1000))
                .all()
                .map(HotelVO::toVo)
                .collectList();
    }

    /**
     * 获取酒店统计信息
     *
     * @return
     */
    public Mono<HotelStatisticsVO> getStatistics(HotelSearchReq hotelSearchReq) {
        // 预过滤并构建查询条件
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> preFilterAndBuildCriteria(hotelSearchReq, headerInfo)
                        .flatMap(criteria -> {
                            // 执行查询并计算统计信息
                            return fetchAndCalculateStatistics(Query.query(criteria));
                        })
                        .doOnSuccess(statistics -> log.info("获取统计信息成功: {}, req={}", statistics, hotelSearchReq))
                        .onErrorResume(e -> {
                            log.error("获取统计信息失败: req={}, error={}", hotelSearchReq, e.getMessage(), e);
                            return Mono.just(new HotelStatisticsVO());
                        }));
    }

    /**
     * 查询酒店并计算统计信息
     */
    private Mono<HotelStatisticsVO> fetchAndCalculateStatistics(Query query) {
        return r2dbcEntityTemplate.select(HdsHotelInfoEntity.class)
                .matching(query)
                .all()
                .collectList()
                .flatMap(hotels -> {
                    if (hotels.isEmpty()) {
                        return Mono.just(new HotelStatisticsVO());
                    }

                    // 提取酒店编码
                    Set<String> hotelCodes = hotels.stream()
                            .map(HdsHotelInfoEntity::getHotelCode)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toSet());

                    // 提取商户ID
                    Set<String> merchantIds = hotels.stream()
                            .map(HdsHotelInfoEntity::getMerchantId)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toSet());

                    Mono<Long> merchantCountMono = merchantIds.isEmpty()
                            ? Mono.just(0L)
                            : r2dbcEntityTemplate.select(HdsMerchantEntity.class)
                            .matching(Query.query(Criteria.where(HdsMerchantFieldEnum.merchant_id.name()).in(merchantIds)))
                            .count();

                    Mono<Long> deviceCountMono = hotelCodes.isEmpty()
                            ? Mono.just(0L)
                            : r2dbcEntityTemplate.select(HdsDeviceEntity.class)
                            .matching(Query.query(Criteria.where(HdsDeviceFieldEnum.hotel_code.name()).in(hotelCodes)
                                    .and(HdsDeviceFieldEnum.device_app_type.name()).not(DeviceTypeEnum.FRONT_APP.getCode())))
                            .count();


                    // 组装结果
                    return Mono.zip(
                            Mono.just(hotels.size()),
                            merchantCountMono,
                            deviceCountMono
                    ).map(tuple -> {
                        long hotelCount = tuple.getT1();
                        long merchantCount = tuple.getT2();
                        long deviceCount = tuple.getT3();

                        HotelStatisticsVO vo = new HotelStatisticsVO();
                        vo.setTotalHotelCount(hotelCount);
                        vo.setTotalMerchantCount(merchantCount);
                        vo.setTotalDeviceCount(deviceCount);
                        return vo;
                    });
                });
    }

    /**
     * 构建查询条件
     *
     * @param hotelSearchReq
     * @return
     */
    private Criteria buildBaseCriteria(HotelSearchReq hotelSearchReq) {
        Criteria criteria = Criteria.empty();

        if (StringUtils.isNotBlank(hotelSearchReq.getBrandName())) {
            criteria = criteria.and(HdsHotelInfoFieldEnum.brand_name.name()).like("%" + hotelSearchReq.getBrandName().trim() + "%");
        }

        // 处理hotel_name和hotel_code的"或"关系
        boolean hasHotelNameOrCode = StringUtils.isNotBlank(hotelSearchReq.getHotelName()) ||
                StringUtils.isNotBlank(hotelSearchReq.getHotelCode());

        if (hasHotelNameOrCode) {
            Criteria hotelCriteria = Criteria.empty();

            if (StringUtils.isNotBlank(hotelSearchReq.getHotelName())) {
                hotelCriteria = hotelCriteria.or(HdsHotelInfoFieldEnum.hotel_name.name())
                        .like("%" + hotelSearchReq.getHotelName().trim() + "%");
            }

            if (StringUtils.isNotBlank(hotelSearchReq.getHotelCode())) {
                hotelCriteria = hotelCriteria.or(HdsHotelInfoFieldEnum.hotel_code.name())
                        .like("%" + hotelSearchReq.getHotelCode().trim() + "%");
            }

            criteria = criteria.and(hotelCriteria);
        }

        // 处理aiProductType字段 - 匹配逗号分隔的列表
        if (StringUtils.isNotBlank(hotelSearchReq.getAiProductType())) {
            String aiProductType = hotelSearchReq.getAiProductType().trim();

            // 方案1: 使用LIKE操作符（适合包含在任何位置的情况）
            // 通过构建匹配模式来查找完整值: ",值," 或 "值," 或 ",值" 或 "值"
            criteria = criteria.and(HdsHotelInfoFieldEnum.ai_product_types.name())
                    .like("%" + aiProductType + "%");
        }

        return criteria;
    }

    /**
     * 根据ID查询酒店
     */
    @RequireHotelAccess(resourceType = RequireHotelAccess.ResourceType.HOTEL)
    public Mono<HotelVO> findById(Integer id) {
        Preconditions.checkArgument(Objects.nonNull(id), "id must not be null");
        return hotelRepository.findById(id)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "hotel not found")))
                .flatMap(hotel -> {
                    HotelVO hotelVO = HotelVO.toVo(hotel);

                    Mono<Boolean> hasPurchasedPackageMono = orderService.countByHotelCodeAndStatus(hotel.getHotelCode(), OrderStatusEnum.PAID.getCode())
                            .map(count -> count > 0);
                    // 如果酒店没有关联商户，直接返回酒店VO
                    if (StringUtils.isBlank(hotel.getMerchantId())) {
                        return hasPurchasedPackageMono
                                .map(hasPurchasedPackage -> {
                                    hotelVO.setHasPurchasedPackage(hasPurchasedPackage);
                                    return hotelVO;
                                });

                    }
                    // 查询关联的商户信息和订单信息
                    Mono<HdsMerchantEntity> merchantMono = merchantService.findByMerchantId(hotel.getMerchantId());
                    return Mono.zip(merchantMono, hasPurchasedPackageMono)
                            .map(tuple -> {
                                HdsMerchantEntity merchant = tuple.getT1();
                                Boolean hasPurchasedPackage = tuple.getT2();

                                // 设置商户相关信息
                                hotelVO.setMerchantName(merchant.getMerchantName());
                                hotelVO.setMerchantShortName(merchant.getSubjectName());
                                hotelVO.setHasPurchasedPackage(hasPurchasedPackage);
                                return hotelVO;
                            })
                            .defaultIfEmpty(hotelVO);
                });

    }

    public Mono<HotelVO> findByHotelCode(String hotelCode) {
        Preconditions.checkArgument(StringUtils.isNotEmpty(hotelCode), "hotelCode must not be null");
        return hotelRepository.findByHotelCode(hotelCode)
                .map(HotelVO::toVo)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "hotel not found")));
    }

    /**
     * 根据酒店编码列表查询酒店信息
     *
     * @param hotelCodes 酒店编码列表
     * @return 酒店信息列表
     */
    public Flux<HotelVO> findByHotelCodes(List<String> hotelCodes) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(hotelCodes), "hotelCodes must not be null");
        return hotelRepository.findByHotelCodeIn(hotelCodes)
                .map(HotelVO::toVo)
                .switchIfEmpty(Flux.empty());
    }

    /**
     * 构建门店实体
     *
     * @param req            请求体
     * @param headerInfo     请求头信息
     * @param brandEntity    品牌实体
     * @param merchantEntity 商户实体
     * @param code           门店编码
     * @return
     */
    private HdsHotelInfoEntity buildHdsHotelInfoEntity(HotelSaveReq req, HeaderUtils.HeaderInfo headerInfo,
                                                       HdsBrandEntity brandEntity, HdsMerchantEntity merchantEntity, String code) {
        // 基础信息设置
        HdsHotelInfoEntity hotelEntity = new HdsHotelInfoEntity();
        BeanUtils.copyProperties(req, hotelEntity);
        hotelEntity.setHotelCode(code);
        hotelEntity.setBookFlag(1);

        if (Objects.equals(req.getSource(),1)) {
            hotelEntity.setStatus(2);
        }else {
            hotelEntity.setStatus(1);
        }
        hotelEntity.setGaodeLongitude(req.getLongitude());
        hotelEntity.setGaodeLatitude(req.getLatitude());
        hotelEntity.setCreatedBy(UserUtils.getUserId(headerInfo));
        hotelEntity.setCreatedByName(UserUtils.getUserName(headerInfo));
        hotelEntity.setCreatedAt(LocalDateTime.now());

        String hotelLogo = req.getHotelLogo();
        if (StringUtils.isBlank(req.getHotelLogo())) {
            hotelLogo = commonProperties.getDefaultHotelLogo();
        }
        hotelEntity.setHotelLogo(hotelLogo);

        // 设置关联信息
        if (Objects.nonNull(brandEntity)) {
            hotelEntity.setBrandCode(brandEntity.getBrandCode());
        }
        if (Objects.nonNull(merchantEntity)) {
            hotelEntity.setMerchantId(merchantEntity.getMerchantId());
        }

        hotelEntity.setSource(req.getSource());

        if (Objects.equals(req.getSource(), 0)) {
            if (CollectionUtils.isNotEmpty(req.getAiProductTypes())) {
                hotelEntity.setAiProductTypes(String.join(",", req.getAiProductTypes()));

                if (req.getAiProductTypes().contains(AiProductTypeEnum.OTA_AGENT.getCode())) {
                    req.setOtaExtendMonths(1);
                    hotelEntity.setOtaExtendMonths(1);
                }
            }
        }
        hotelEntity.setRowStatus(1);
        return hotelEntity;
    }

    /**
     * 更新酒店实体
     */
    private void updateHdsHotelInfoEntity(HdsHotelInfoEntity existingHotel, HdsBrandEntity brand, HdsMerchantEntity merchant, HotelSaveReq
            req, HeaderUtils.HeaderInfo headerInfo) {

        Integer source = req.getSource();
        Integer hotelSource = existingHotel.getSource();

        // 门店名称更新逻辑-ota来源不更新门店名称
        if (StringUtils.isNotBlank(req.getHotelName()) && Objects.equals(source, 0) && Objects.equals(hotelSource, 0)) {
            existingHotel.setHotelName(req.getHotelName());
        }
        if (StringUtils.isNotBlank(req.getHotelNamePinYin()) && Objects.equals(source, 0)) {
            existingHotel.setHotelNamePinYin(req.getHotelNamePinYin());
        }

        // 基础信息更新
        if (Objects.equals(source, 0)) {
            // source=0 按现有逻辑直接更新
            existingHotel.setHotelType(req.getHotelType());
            existingHotel.setTotalRoom(req.getTotalRoom());
            existingHotel.setMainPerson(req.getMainPerson());
            existingHotel.setPhone(req.getPhone());
            existingHotel.setEmail(req.getEmail());
            existingHotel.setCountryCode(req.getCountryCode());
            existingHotel.setProvinceCode(req.getProvinceCode());
            existingHotel.setCityCode(req.getCityCode());
            existingHotel.setDistrictCode(req.getDistrictCode());
            existingHotel.setSubjectName(req.getSubjectName());
            existingHotel.setCountryName(req.getCountryName());
            existingHotel.setProvinceName(req.getProvinceName());
            existingHotel.setCityName(req.getCityName());
            existingHotel.setDistrictName(req.getDistrictName());
            existingHotel.setAddress(req.getAddress());
            existingHotel.setCtripEbkUrl(req.getCtripEbkUrl());
            existingHotel.setHotelLogo(req.getHotelLogo());
            existingHotel.setGaodeLongitude(req.getLongitude());
            existingHotel.setGaodeLatitude(req.getLatitude());
            if (Objects.nonNull(req.getSosSwitch())) {
                existingHotel.setSosSwitch(req.getSosSwitch());
            }
            if (StringUtils.isNotBlank(req.getCallCommand())){
                existingHotel.setCallCommand(req.getCallCommand());
            }
            if (Objects.nonNull(req.getDialogSwitch())){
                existingHotel.setDialogSwitch(req.getDialogSwitch());
            }
            if (StringUtils.isNotBlank(req.getFrontPhone())) {
                existingHotel.setFrontPhone(req.getFrontPhone());
            }
        } else if (Objects.equals(source, 1)) {
            // source=1 只在值不为空时更新
            if (Objects.nonNull(req.getHotelType())) {
                existingHotel.setHotelType(req.getHotelType());
            }
            if (Objects.nonNull(req.getTotalRoom())) {
                existingHotel.setTotalRoom(req.getTotalRoom());
            }
            if (StringUtils.isNotBlank(req.getMainPerson())) {
                existingHotel.setMainPerson(req.getMainPerson());
            }
            if (StringUtils.isNotBlank(req.getPhone())) {
                existingHotel.setPhone(req.getPhone());
            }
            if (StringUtils.isNotBlank(req.getFrontPhone())) {
                existingHotel.setFrontPhone(req.getFrontPhone());
            }
            if (StringUtils.isNotBlank(req.getEmail())) {
                existingHotel.setEmail(req.getEmail());
            }
            if (StringUtils.isNotBlank(req.getCountryCode())) {
                existingHotel.setCountryCode(req.getCountryCode());
            }
            if (StringUtils.isNotBlank(req.getProvinceCode())) {
                existingHotel.setProvinceCode(req.getProvinceCode());
            }
            if (StringUtils.isNotBlank(req.getCityCode())) {
                existingHotel.setCityCode(req.getCityCode());
            }
            if (StringUtils.isNotBlank(req.getDistrictCode())) {
                existingHotel.setDistrictCode(req.getDistrictCode());
            }
            if (StringUtils.isNotBlank(req.getSubjectName())) {
                existingHotel.setSubjectName(req.getSubjectName());
            }
            if (StringUtils.isNotBlank(req.getCountryName())) {
                existingHotel.setCountryName(req.getCountryName());
            }
            if (StringUtils.isNotBlank(req.getProvinceName())) {
                existingHotel.setProvinceName(req.getProvinceName());
            }
            if (StringUtils.isNotBlank(req.getCityName())) {
                existingHotel.setCityName(req.getCityName());
            }
            if (StringUtils.isNotBlank(req.getDistrictName())) {
                existingHotel.setDistrictName(req.getDistrictName());
            }
            if (StringUtils.isNotBlank(req.getAddress())) {
                existingHotel.setAddress(req.getAddress());
            }
            if (StringUtils.isNotBlank(req.getCtripEbkUrl())) {
                existingHotel.setCtripEbkUrl(req.getCtripEbkUrl());
            }
            if (StringUtils.isNotBlank(req.getHotelLogo())) {
                existingHotel.setHotelLogo(req.getHotelLogo());
            }
            if (StringUtils.isNotBlank(req.getLongitude())) {
                existingHotel.setGaodeLongitude(req.getLongitude());
            }
            if (StringUtils.isNotBlank(req.getLatitude())) {
                existingHotel.setGaodeLatitude(req.getLatitude());
            }
        }

        // 品牌信息处理（共同逻辑）
        if (Objects.equals(source, 0)) {
            // source=0 按现有逻辑
            if (StringUtils.isBlank(req.getBrandName())) {
                existingHotel.setBrandCode(null);
                existingHotel.setBrandName(null);
            }
            if (Objects.nonNull(brand) && StringUtils.isNotBlank(brand.getBrandCode())) {
                existingHotel.setBrandCode(brand.getBrandCode());
                existingHotel.setBrandName(brand.getBrandName());
            }
        } else if (Objects.equals(source, 1)) {
            // source=1 只在值不为空时处理
            if (StringUtils.isNotBlank(req.getBrandName()) && Objects.nonNull(brand) && StringUtils.isNotBlank(brand.getBrandCode())) {
                existingHotel.setBrandCode(brand.getBrandCode());
                existingHotel.setBrandName(brand.getBrandName());
            }
        }

        // 商户信息处理（共同逻辑）
        if (Objects.equals(source, 0)) {
            // source=0 按现有逻辑
            if (StringUtils.isBlank(req.getMerchantName()) && StringUtils.isBlank(req.getMerchantShortName())) {
                existingHotel.setMerchantId(null);
            }
            if (Objects.nonNull(merchant) && StringUtils.isNotBlank(merchant.getMerchantId())) {
                existingHotel.setMerchantId(merchant.getMerchantId());
                existingHotel.setMerchantName(merchant.getMerchantName());
            }
        } else if (Objects.equals(source, 1)) {
            // source=1 只在值不为空时处理
            if ((StringUtils.isNotBlank(req.getMerchantName()) || StringUtils.isNotBlank(req.getMerchantShortName()))
                    && Objects.nonNull(merchant) && StringUtils.isNotBlank(merchant.getMerchantId())) {
                existingHotel.setMerchantId(merchant.getMerchantId());
                existingHotel.setMerchantName(merchant.getMerchantName());
            }
        }

        // AI产品类型处理
        if (CollectionUtils.isNotEmpty(req.getAiProductTypes())) {
            existingHotel.setAiProductTypes(String.join(",", req.getAiProductTypes()));
        }

        // OTA延期月数处理
        if (Objects.nonNull(req.getOtaExtendMonths())) {
            existingHotel.setOtaExtendMonths(Objects.nonNull(existingHotel.getOtaExtendMonths()) ?
                    existingHotel.getOtaExtendMonths() + req.getOtaExtendMonths() : req.getOtaExtendMonths());
        }

        // 更新审计字段（共同逻辑）
        existingHotel.setUpdatedBy(UserUtils.getUserId(headerInfo));
        existingHotel.setUpdatedByName(UserUtils.getUserName(headerInfo));
        existingHotel.setUpdatedAt(LocalDateTime.now());
    }

    /**
     * 更新设备状态
     */
    public Mono<Boolean> updateDeviceStatus(UpdateDeviceStatusReq updateDeviceStatusReq) {
        Instant startTime = Instant.now();
        ValidatorUtils.validateUpdateDeviceStatusReq(updateDeviceStatusReq);
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> executeTransferWithinTransactionUpdate(startTime, updateDeviceStatusReq, headerInfo))
                .onErrorResume(ex -> {
                    if (ex instanceof BusinessException) {
                        // 业务异常直接向上抛出
                        return Mono.error(ex);
                    }
                    log.error("更新设备状态失败: {}", ex.getMessage(), ex);
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, ex.getMessage()));
                });
    }

    /**
     * 执行更新事务
     */
    private Mono<Boolean> executeTransferWithinTransactionUpdate(Instant startTime, UpdateDeviceStatusReq req, HeaderUtils.HeaderInfo
            headerInfo) {
        boolean isEnable = UpdateDeviceStatusEnum.ENABLE.getCode().equals(req.getUpdateDeviceStatus());

        // 1. 查询酒店是否存在且状态正确
        return hotelRepository.findAllById(req.getIdList())
                .collectList()
                .flatMap(hotels -> {
                    if (hotels.isEmpty()) {
                        log.info("没有需要处理的设备: hotelIds={}", req.getIdList());
                        return Mono.error(new BusinessException(ResultCode.NOT_FOUND, "没有需要处理的设备"));
                    }

                    // 2. 查询需要更新的设备
                    List<String> hotelCodes = hotels.stream()
                            .map(HdsHotelInfoEntity::getHotelCode)
                            .collect(Collectors.toList());

                    int currentDeviceStatus = isEnable ? DeviceStatusEnum.DEACTIVATED.getCode() : DeviceStatusEnum.ONLINE.getCode();

                    Mono<List<HdsDeviceEntity>> byHotelCodesAndDeviceStatus = deviceService.findByHotelCodeInAndDeviceStatusEquals(hotelCodes, currentDeviceStatus);

                    return byHotelCodesAndDeviceStatus
                            .flatMap(devices ->
                                    executeUpdateTransaction(startTime, req, hotels, devices, isEnable, headerInfo))
                            .flatMap(result -> {
                                if (result && !isEnable) {
                                    // 异步发送酒店不可用消息
                                    return Flux.fromIterable(hotelCodes)
                                            .flatMap(hotelCode -> hotelServiceExpiryHandler.sendHotelServiceExpiryMessage(hotelCode, ChannelEventEnum.hotel_unavailable)
                                                    .doOnError(e -> log.error("发送酒店服务到期消息失败: hotelCode={}, error={}", hotelCode, e.getMessage())))
                                            .then(Mono.just(Boolean.TRUE));
                                }
                                return Mono.just(result);
                            });
                });
    }

    /**
     * 执行更新事务操作
     */
    private Mono<Boolean> executeUpdateTransaction(
            Instant startTime,
            UpdateDeviceStatusReq req,
            List<HdsHotelInfoEntity> hotels,
            List<HdsDeviceEntity> beforeDevices,
            boolean isEnable,
            HeaderUtils.HeaderInfo headerInfo) {

        // 1. 准备更新数据
        List<Integer> hotelIds = hotels.stream()
                .map(HdsHotelInfoEntity::getId)
                .collect(Collectors.toList());

        List<Long> deviceIds = beforeDevices.stream()
                .map(HdsDeviceEntity::getId)
                .collect(Collectors.toList());

        List<String> deviceIdList = beforeDevices.stream().map(HdsDeviceEntity::getDeviceId)
                .toList();

        int targetHotelStatus = isEnable ? 1 : 0;
        int targetDeviceStatus = isEnable ? DeviceStatusEnum.ONLINE.getCode() : DeviceStatusEnum.DEACTIVATED.getCode();
        DeviceEventTypeEnum eventType = isEnable ? DeviceEventTypeEnum.ACTIVATION : DeviceEventTypeEnum.DEACTIVATION;
        String operationDesc = isEnable ? "门店启用设备" : "门店停用设备";

        // 2. 创建事务操作
        Mono<Tuple2<Integer, Integer>> transactionMono = Mono.defer(() -> {
            // 更新酒店状态
            Mono<Integer> updateHotel = hotelRepository.updateHotelInfoStatus(
                    hotelIds,
                    targetHotelStatus,
                    headerInfo.getUserId(),
                    headerInfo.getUsername()
            );

            // 更新设备状态
            Mono<Integer> updateDevice = deviceService.updateDeviceStatus(
                    deviceIds,
                    targetDeviceStatus,
                    headerInfo.getUserId(),
                    headerInfo.getUsername()
            );


            return Mono.zip(updateHotel, updateDevice);
        });

        // 3. 执行事务
        return transactionalOperator.transactional(transactionMono)
                .flatMap(results -> {
                    if (CollectionUtils.isEmpty(deviceIds)) {
                        return Mono.empty();
                    }
                    // 发送设备离线消息
                    Mono<Void> offlineMsgMono = Mono.empty();
                    if (!isEnable) {
                        List<Mono<Void>> msgMonos = hotels.stream()
                                .map(hotel -> deviceMonitorHandler.sendDeviceOfflineMessage(
                                        hotel.getHotelCode(), ChannelEventEnum.device_offline))
                                .toList();
                        offlineMsgMono = Mono.when(msgMonos);
                    }

                    // 发送RTC消息
                    return offlineMsgMono.then(deviceService.sendDeviceRtcMessage(deviceIdList, isEnable ? "true" : "false").thenReturn(Boolean.TRUE))
                            .flatMap(result -> {
                                // 4. 查询更新后的设备并记录日志
                                return deviceService.findAllById(deviceIds)
                                        .flatMap(afterDevices ->
                                                handleUpdateSuccess(
                                                        startTime,
                                                        hotels,
                                                        beforeDevices,
                                                        afterDevices,
                                                        eventType,
                                                        operationDesc,
                                                        req,
                                                        headerInfo
                                                )
                                        );
                            });
                })
                .thenReturn(true)
                .onErrorResume(e -> {
                    handleUpdateError(e, hotels, beforeDevices);
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "设备状态更新失败"));
                });
    }

    /**
     * 处理更新成功
     */
    private Mono<Void> handleUpdateSuccess(
            Instant startTime,
            List<HdsHotelInfoEntity> hotels,
            List<HdsDeviceEntity> beforeDevices,
            List<HdsDeviceEntity> afterDevices,
            DeviceEventTypeEnum eventType,
            String operationDesc,
            UpdateDeviceStatusReq req,
            HeaderUtils.HeaderInfo headerInfo) {

        try {
            // 1. 准备酒店映射
            Map<String, HdsHotelInfoEntity> hotelMap = hotels.stream()
                    .collect(Collectors.toMap(
                            HdsHotelInfoEntity::getHotelCode,
                            Function.identity(),
                            (existing, replacement) -> existing
                    ));

            // 2. 创建设备生命周期日志
            List<HdsDeviceLogEntity> deviceLogs = afterDevices.stream()
                    .map(device -> deviceLogService.createDeviceLog(device, hotelMap, eventType, operationDesc, headerInfo))
                    .collect(Collectors.toList());

            // 3. 创建设备操作日志上下文列表
            List<LoggingContext> loggingContexts = new ArrayList<>();
            for (int i = 0; i < afterDevices.size(); i++) {
                HdsDeviceEntity beforeDevice = beforeDevices.get(i);
                HdsDeviceEntity afterDevice = afterDevices.get(i);

                LoggingContext context = LoggingContext.builder()
                        .oprParams(req)
                        .businessId(afterDevice.getId())
                        .oprTime(LocalDateTime.ofInstant(startTime, ZoneId.systemDefault()))
                        .businessType(BussinessTypeEnum.DEVICE)
                        .operationType(OperationTypeEnum.UPDATE)
                        .methodName("updateDeviceStatus")
                        .headerInfo(headerInfo)
                        .oprResult("SUCCESS")
                        .beforeObj(beforeDevice)
                        .afterObj(afterDevice)
                        .executionTime(Duration.between(startTime, Instant.now()).toMillis())
                        .oprContent(operationDesc)
                        .build();

                loggingContexts.add(context);
            }

            // 4. 批量保存设备日志和操作日志
            return Mono.zip(
                    deviceLogService.saveAll(deviceLogs),
                    Flux.fromIterable(loggingContexts)
                            .flatMap(operationLogUtil::recordSuccess)
                            .collectList()
            ).then();
        } catch (Exception ex) {
            log.error("记录操作日志失败，设备ID: {}, 错误: {}",
                    afterDevices.stream().map(HdsDeviceEntity::getDeviceId).collect(Collectors.toList()), ex.getMessage(), ex);
            return Mono.empty();
        }
    }


    /**
     * 处理更新错误
     */
    private void handleUpdateError(Throwable
                                           e, List<HdsHotelInfoEntity> hotels, List<HdsDeviceEntity> devices) {
        log.error("状态更新事务执行失败: hotelIds={}, deviceIds={}, error={}",
                hotels.stream().map(HdsHotelInfoEntity::getId).collect(Collectors.toList()),
                devices.stream().map(HdsDeviceEntity::getId).collect(Collectors.toList()),
                e.getMessage(),
                e);
    }

    /**
     * 根据关键词搜索酒店编码列表
     *
     * @param keyword 关键词（可以是商户名称、品牌名称、门店名称、酒店编码等）
     * @return 酒店编码列表
     */
    public Mono<List<String>> searchHotelCodes(String keyword) {
        if (StringUtils.isEmpty(keyword)) {
            return Mono.just(List.of());
        }

        String likeKeyword = "%" + keyword + "%";
        Criteria criteria = Criteria.empty()
                .or(Criteria.where(HdsHotelInfoFieldEnum.hotel_code.name()).like(likeKeyword))
                .or(Criteria.where(HdsHotelInfoFieldEnum.hotel_name.name()).like(likeKeyword))
                .or(Criteria.where(HdsHotelInfoFieldEnum.brand_name.name()).like(likeKeyword))
                .or(Criteria.where(HdsHotelInfoFieldEnum.merchant_name.name()).like(likeKeyword));

        return r2dbcEntityTemplate.select(Query.query(criteria), HdsHotelInfoEntity.class)
                .map(HdsHotelInfoEntity::getHotelCode)
                .collectList();
    }

    public Mono<List<HdsHotelInfoEntity>> batchQueryHotels(Set<String> hotelCodes) {
        if (CollectionUtils.isEmpty(hotelCodes)) {
            return Mono.empty();
        }
        Criteria criteria = Criteria.where(HdsHotelInfoFieldEnum.hotel_code.name()).in(hotelCodes);
        return r2dbcEntityTemplate.select(Query.query(criteria), HdsHotelInfoEntity.class)
                .collectList();
    }

    /**
     * 搜索门店数量
     */
    public Mono<Long> getEmployeeHotelCount(HotelSearchReq req, HeaderUtils.HeaderInfo headerInfo) {
        return buildEmployeeHotelCriteria(req, headerInfo)
                .flatMap(criteria -> {
                    // 执行计数查询
                    return r2dbcEntityTemplate.count(Query.query(criteria), HdsHotelInfoEntity.class);
                })
                .defaultIfEmpty(0L)
                .doOnSuccess(count -> log.info("门店计数成功: count={}, req={}", count, req))
                .onErrorResume(e -> {
                    log.error("门店计数失败: req={}, error={}", req, e.getMessage(), e);
                    return Mono.just(0L);
                });
    }

    /**
     * 搜索门店列表
     */
    public Mono<List<EmployeeHotelSearchVO>> getEmployeeHotelSearch(HotelSearchReq req, HeaderUtils.HeaderInfo headerInfo) {
        // 预过滤并构建查询条件
        return buildEmployeeHotelCriteria(req, headerInfo)
                .flatMap(criteria -> {
                    // 添加排序和分页
                    Query query = Query.query(criteria)
                            .sort(Sort.by(Sort.Direction.DESC, HdsHotelInfoFieldEnum.created_at.name()))
                            .limit(req.getPageSize())
                            .offset((long) (req.getCurrent() - 1) * req.getPageSize());

                    // 执行查询
                    return r2dbcEntityTemplate.select(HdsHotelInfoEntity.class)
                            .matching(query)
                            .all()
                            .map(EmployeeHotelSearchVO::toVO)
                            .collectList();
                })
                .doOnSuccess(list -> log.info("门店查询成功: size={}, req={}", list.size(), req))
                .onErrorResume(e -> {
                    log.error("门店查询失败: req={}, error={}", req, e.getMessage(), e);
                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "门店查询失败"));
                });
    }

    /**
     * 根据查询条件预先过滤相关数据，构建更精确的查询条件
     */
    private Mono<Criteria> buildEmployeeHotelCriteria(HotelSearchReq hotelSearchReq, HeaderUtils.HeaderInfo headerInfo) {
        // 基础条件
        Criteria baseCriteria = buildBaseCriteria(hotelSearchReq);
        baseCriteria = baseCriteria.and(Criteria.where(HdsHotelInfoFieldEnum.status.name()).is(1))
                .and(Criteria.where(HdsHotelInfoFieldEnum.row_status.name()).is(1));

        Criteria finalBaseCriteria = baseCriteria;
        return hdsEmployeeService.needFilterByEmployeeId(headerInfo)
                .flatMap(needFilter -> {
                    if (Boolean.TRUE.equals(needFilter)) {
                        // 需要过滤员工ID
                        return hdsEmployeeHotelService.preFilterByEmployeeId(headerInfo, finalBaseCriteria, true);
                    } else {
                        return Mono.just(finalBaseCriteria);
                    }
                });
    }

    public Mono<Boolean> checkIsNewUser(HeaderUtils.HeaderInfo headerInfo) {
        return hdsEmployeeHotelService.listByEmployeeId(Integer.valueOf(headerInfo.getUserId()), EmployeeStatusEnum.ACTIVE.getCode(), null)
                .map(CollectionUtils::isEmpty)
                .defaultIfEmpty(true);
    }

    /**
     * 搜索门店数量
     */
    public Mono<Long> searchActiveCount(HotelSearchReq req, HeaderUtils.HeaderInfo headerInfo) {
        Criteria criteria = buildSearchActiveBaseCriteria(req);
        return r2dbcEntityTemplate.count(Query.query(criteria), HdsHotelInfoEntity.class)
                .defaultIfEmpty(0L)
                .onErrorResume(e -> {
                    log.error("门店计数失败: req={}, error={}", req, e.getMessage(), e);
                    return Mono.just(0L);
                });
    }

    /**
     * 搜索门店列表
     */
    public Mono<List<HotelSearchVO>> searchActive(HotelSearchReq req, HeaderUtils.HeaderInfo headerInfo) {
        Criteria criteria = buildSearchActiveBaseCriteria(req);
        Query query = buildQuery(req, criteria);
        // 预过滤并构建查询条件
        return r2dbcEntityTemplate.select(HdsHotelInfoEntity.class)
                .matching(query)
                .all()
                .collectList()
                .flatMap(this::enrichHotelsWithRelatedData);
    }

    private Criteria buildSearchActiveBaseCriteria(HotelSearchReq req) {
        Criteria criteria = Criteria.where(HdsHotelInfoFieldEnum.status.name()).is(1)
                .and(Criteria.where(HdsHotelInfoFieldEnum.row_status.name()).is(1));
        if (StringUtils.isNotBlank(req.getAiProductType())) {
            String aiProductType = req.getAiProductType().trim();
            criteria = criteria.and(HdsHotelInfoFieldEnum.ai_product_types.name())
                    .like("%" + aiProductType + "%");
        }
        return criteria;
    }

    private Query buildQuery(HotelSearchReq req, Criteria criteria) {
        return Query.query(criteria)
                .sort(Sort.by(Sort.Direction.DESC, HdsHotelInfoFieldEnum.created_at.name()))
                .limit(req.getPageSize())
                .offset((long) (req.getCurrent() - 1) * req.getPageSize());
    }

    /**
     * @param hotelCode  酒店编码
     * @param expireTime 新的到期时间
     * @return 更新结果
     */
    public Mono<Boolean> updateOtaExpireTime(String hotelCode, LocalDateTime expireTime, HdsOrderEntity orderEntity) {
        if (StringUtils.isBlank(hotelCode) || expireTime == null) {
            log.warn("Invalid parameters for updateOtaExpireTime: hotelCode={}, expireTime={}", hotelCode, expireTime);
            return Mono.just(false);
        }

        log.info("Updating OTA expire time for hotel: {}, new expire time: {}", hotelCode, expireTime);

        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> hotelRepository.findByHotelCode(hotelCode)
                        .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "Hotel not found: " + hotelCode)))
                        .flatMap(hotel -> {
                            // 保存原始数据，用于流水记录
                            LocalDateTime originalInitFinishedAt = hotel.getInitFinishedAt();
                            Integer originalOtaExtendMonths = hotel.getOtaExtendMonths();
                            Integer originalOtaRewardMonths = hotel.getOtaRewardMonths();

                            // 计算当前到期时间
                            LocalDateTime currentExpireTime = calculateCurrentExpireTime(hotel);

                            // 检查服务是否已过期
                            LocalDateTime now = LocalDateTime.now();
                            boolean isExpired = currentExpireTime == null || now.isAfter(currentExpireTime);

                            // 计算新的到期时间
                            LocalDateTime newExpireTime = calculateNewExpireTime(currentExpireTime, orderEntity);

                            log.info("Updating OTA expire time for hotel: {}, current: {}, new: {}, isExpired: {}",
                                    hotelCode, currentExpireTime, newExpireTime, isExpired);

                            // 更新酒店信息
                            updateHotelInfo(hotel, newExpireTime, orderEntity, isExpired);

                            // 创建时间流水记录
                            HdsAgentTimeFlowEntity timeFlowEntity = createTimeFlowEntity(
                                    hotel, currentExpireTime, newExpireTime, headerInfo, orderEntity,
                                    originalInitFinishedAt, originalOtaExtendMonths, originalOtaRewardMonths, isExpired);

                            // 执行事务操作
                            return transactionalOperator.transactional(
                                    Mono.zip(
                                            hotelRepository.save(hotel),
                                            agentTimeFlowService.save(timeFlowEntity)
                                    )
                            ).map(tuple -> {
                                log.info("Successfully updated OTA expire time for hotel: {}", hotelCode);
                                return true;
                            });
                        })
                )
                .onErrorResume(e -> {
                    log.error("Failed to update OTA expire time for hotel: {}", hotelCode, e);
                    return Mono.just(false);
                });
    }

    /**
     * 计算当前到期时间
     */
    private LocalDateTime calculateCurrentExpireTime(HdsHotelInfoEntity hotel) {
        if (hotel.getInitFinishedAt() == null) {
            return null;
        }

        LocalDateTime currentExpireTime = hotel.getInitFinishedAt();

        if (Objects.nonNull(hotel.getOtaExtendMonths())) {
            currentExpireTime = currentExpireTime.plusMonths(hotel.getOtaExtendMonths());
        }

        if (Objects.nonNull(hotel.getOtaRewardMonths())) {
            currentExpireTime = currentExpireTime.plusMonths(hotel.getOtaRewardMonths());
        }

        return currentExpireTime;
    }

    /**
     * 计算新的到期时间
     * 1. 服务已过期：按当前时间开始计算，增加【计费周期】核算到期时间
     * 2. 服务未过期：按到期时间开始计算，增加【计费周期】核算到期时间
     */
    private LocalDateTime calculateNewExpireTime(LocalDateTime currentExpireTime, HdsOrderEntity orderEntity) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime baseTime;

        // 判断服务是否已过期
        if (currentExpireTime == null || now.isAfter(currentExpireTime)) {
            // 服务已过期，从当前时间开始计算
            baseTime = now;
        } else {
            // 服务未过期，从到期时间开始计算
            baseTime = currentExpireTime;
        }

        // 获取计费周期
        int billingPeriodMonths = getBillingPeriodMonths(orderEntity);

        // 计算到期时间
        return baseTime.plusMonths(billingPeriodMonths);
    }

    /**
     * 从订单中获取计费周期（月数）
     */
    private int getBillingPeriodMonths(HdsOrderEntity orderEntity) {
        Integer periodType = Optional.ofNullable(orderEntity.getPeriodType()).orElse(1);

        // 根据周期类型转换为实际月数
        return switch (periodType) {
            case 1 -> // 月度
                    1;
            case 2 -> // 季度
                    3;
            case 3 -> // 年度
                    12;
            default -> {
                log.warn("Unknown period type: {}, using default (1 month)", periodType);
                yield 1;
            }
        };
    }

    /**
     * 更新酒店信息
     */
    private void updateHotelInfo(HdsHotelInfoEntity hotel, LocalDateTime newExpireTime,
                                 HdsOrderEntity orderEntity, boolean isExpired) {
        // 初始化时间处理
        if (hotel.getInitFinishedAt() == null) {
            hotel.setInitFinishedAt(LocalDateTime.now());
        }

        // 确保AI产品类型包含OTA_AGENT
        updateAiProductTypes(hotel);

        // 获取计费周期
        int billingPeriodMonths = getBillingPeriodMonths(orderEntity);

        if (isExpired) {
            // 服务已过期，重置基准时间为当前时间
            hotel.setInitFinishedAt(LocalDateTime.now());

            // 设置扩展月数为新购买的月数
            hotel.setOtaExtendMonths(billingPeriodMonths);
            hotel.setOtaRewardMonths(0);

        } else {
            // 服务未过期，保留原基准时间

            // 获取当前扩展月数
            int currentExtendMonths = hotel.getOtaExtendMonths() != null ? hotel.getOtaExtendMonths() : 0;

            // 设置新的扩展月数（当前扩展月数 + 新购买的月数）
            hotel.setOtaExtendMonths(currentExtendMonths + billingPeriodMonths);
        }

        // 设置审计字段
        hotel.setUpdatedAt(LocalDateTime.now());
    }

    /**
     * 更新AI产品类型
     */
    private void updateAiProductTypes(HdsHotelInfoEntity hotel) {
        if (StringUtils.isBlank(hotel.getAiProductTypes())) {
            hotel.setAiProductTypes(AiProductTypeEnum.OTA_AGENT.getCode());
        } else if (!hotel.getAiProductTypes().contains(AiProductTypeEnum.OTA_AGENT.getCode())) {
            Set<String> aiProductTypes = new HashSet<>(Arrays.asList(hotel.getAiProductTypes().split(",")));
            aiProductTypes.add(AiProductTypeEnum.OTA_AGENT.getCode());
            hotel.setAiProductTypes(String.join(",", aiProductTypes));
        }
    }

    /**
     * 创建时间流水记录
     */
    private HdsAgentTimeFlowEntity createTimeFlowEntity(
            HdsHotelInfoEntity hotel,
            LocalDateTime oldExpireTime,
            LocalDateTime newExpireTime,
            HeaderUtils.HeaderInfo headerInfo,
            HdsOrderEntity orderEntity,
            LocalDateTime originalInitFinishedAt,
            Integer originalOtaExtendMonths,
            Integer originalOtaRewardMonths,
            boolean isExpired) {

        HdsAgentTimeFlowEntity entity = new HdsAgentTimeFlowEntity();

        // 设置基本信息
        entity.setHotelCode(hotel.getHotelCode());
        entity.setType(0); // 0-OTA
        entity.setFlowType(AgentFlowTypeEnum.RECHARGE.getCode()); // 5-订单支付
        entity.setOperationType(1); // 1-增加

        // 获取计费周期（月数变更）
        int monthsChanged = getBillingPeriodMonths(orderEntity);
        entity.setMonthsChanged(monthsChanged);

        // 设置扩展月数变更前后的值
        int oldExtendMonths = originalOtaExtendMonths != null ? originalOtaExtendMonths : 0;

        if (isExpired) {
            // 服务已过期，记录重置情况
            entity.setOtaExtendMonthsBefore(oldExtendMonths);
            entity.setOtaExtendMonthsAfter(monthsChanged); // 新值直接为购买的月数
        } else {
            // 服务未过期，记录累加情况
            entity.setOtaExtendMonthsBefore(oldExtendMonths);
            entity.setOtaExtendMonthsAfter(oldExtendMonths + monthsChanged);
        }

        // 设置奖励月数（不变）
        int rewardMonths = originalOtaRewardMonths != null ? originalOtaRewardMonths : 0;
        entity.setOtaRewardMonthsBefore(rewardMonths);
        entity.setOtaRewardMonthsAfter(rewardMonths);

        // 设置到期时间
        entity.setExpirationBefore(oldExpireTime);
        entity.setExpirationAfter(newExpireTime);

        // 设置审计字段
        entity.setCreatedBy(orderEntity.getCreatedBy());
        entity.setCreatedByName(orderEntity.getCreatedByName());
        entity.setCreatedAt(LocalDateTime.now());
        entity.setRowStatus(1);

        return entity;
    }
}
