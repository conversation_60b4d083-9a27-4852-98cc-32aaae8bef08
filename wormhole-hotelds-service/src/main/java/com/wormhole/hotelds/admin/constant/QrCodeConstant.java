package com.wormhole.hotelds.admin.constant;

/**
 * <AUTHOR>
 * @date 2025/4/21 17:25
 */
public class QrCodeConstant {

    public static final String STORAGE_BASE_URL = "wormhole/hotelds/api";

    public static final String WECHAT_QR_CODE_URL = STORAGE_BASE_URL + "/wechat/%s/%s_%d.png";

    public static final String WECHAT_QR_CODE_POSTER_URL = STORAGE_BASE_URL + "/wechat/poster/%s/%s_%d.png";

    public static final String WECHAT_QR_CODE_ZIP_URL = STORAGE_BASE_URL + "/wechat/zip_%d.zip";

    //t=room&k=
    public static final String WECHAT_QR_CODE_URL_PREFIX = "t=room&k=";

    public static final String NORMAL_QR_CODE_URL = STORAGE_BASE_URL + "/qrcode/%s/%s_%d.png";

    public static final String NORMAL_QR_CODE_POSTER_URL = STORAGE_BASE_URL + "/qrcode/poster/%s/%s_%d.png";

    public static final String NORMAL_QR_CODE_ZIP_URL = STORAGE_BASE_URL + "/qrcode/zip_%d.zip";

    public static final int QR_CODE_SIZE = 600;

}
