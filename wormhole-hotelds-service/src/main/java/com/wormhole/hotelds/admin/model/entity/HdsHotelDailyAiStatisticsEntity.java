package com.wormhole.hotelds.admin.model.entity;

import com.wormhole.common.model.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDate;

/**
 * @Author: AI Assistant
 * @Date: 2025-08-15
 * @Description: 酒店每日AI运营统计表实体
 */
@Data
@Table("hds_hotel_daily_ai_statistics")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsHotelDailyAiStatisticsEntity extends BaseEntity {

    /**
     * 主键ID
     */
    @Id
    @Column("id")
    private Long id;

    /**
     * 业务统计日期（统计每天）
     */
    @Column("business_date")
    private LocalDate businessDate;

    /**
     * 酒店代码
     */
    @Column("hotel_code")
    private String hotelCode;

    /**
     * 客诉工单数
     */
    @Column("complaint_ticket_count")
    private Integer complaintTicketCount;

    /**
     * 客诉预警工单数
     */
    @Column("complaint_warning_ticket_count")
    private Integer complaintWarningTicketCount;

    /**
     * OTA差评数
     */
    @Column("ota_negative_review_count")
    private Integer otaNegativeReviewCount;

    /**
     * AI通话数量
     */
    @Column("ai_call_count")
    private Integer aiCallCount;

    /**
     * 回拨通话数量
     */
    @Column("return_call_count")
    private Integer returnCallCount;

    /**
     * 总工单数量
     */
    @Column("ticket_count")
    private Integer ticketCount;

    /**
     * 已完成工单数量
     */
    @Column("completed_ticket_count")
    private Integer completedTicketCount;

    /**
     * 问询总数
     */
    @Column("inquiry_count")
    private Integer inquiryCount;

    /**
     * 问询已完成总数
     */
    @Column("inquiry_completed_count")
    private Integer inquiryCompletedCount;

    /**
     * 客需工单总数
     */
    @Column("service_need_count")
    private Integer serviceNeedCount;

    /**
     * 客需工单已完成总数
     */
    @Column("service_need_completed_count")
    private Integer serviceNeedCompletedCount;

    /**
     * 客诉工单已完成总数
     */
    @Column("complaint_completed_count")
    private Integer complaintCompletedCount;

    /**
     * 紧急事项总数
     */
    @Column("emergency_count")
    private Integer emergencyCount;

    /**
     * 紧急事项工单已完成总数
     */
    @Column("emergency_completed_count")
    private Integer emergencyCompletedCount;

    /**
     * 使用房间数
     */
    @Column("room_use_count")
    private Integer roomUseCount;

    /**
     * 平均通话时长（秒）
     */
    @Column("avg_call_duration_seconds")
    private Float avgCallDurationSeconds;

    /**
     * 文字对话数
     */
    @Column("text_dialogue_count")
    private Integer textDialogueCount;

    /**
     * AI解决数
     */
    @Column("ai_solve_count")
    private Integer aiSolveCount;

    /**
     * 处理时长（秒）
     */
    @Column("avg_complete_duration_seconds")
    private Float avgCompleteDurationSeconds;

    /**
     * 超时工单数
     */
    @Column("overdue_count")
    private Integer overdueCount;
}
