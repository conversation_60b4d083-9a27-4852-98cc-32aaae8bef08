package com.wormhole.hotelds.admin.repository;

import com.wormhole.hotelds.core.model.entity.*;
import io.reactivex.Flowable;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

/**
 * @Author: flx
 * @Date: 2025-03-27 17:58:35
 * @Description: AppVersions
 */
@Repository
public interface AppVersionsRepository extends ReactiveCrudRepository<HdsAppVersionsEntity, Integer> {

    Mono<Boolean> existsByAppVersionAndAppCode(String appVersion, String appCode);
}