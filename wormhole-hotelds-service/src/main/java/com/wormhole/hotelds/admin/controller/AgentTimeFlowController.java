package com.wormhole.hotelds.admin.controller;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.admin.model.req.AgentTimeFlowSearchReq;
import com.wormhole.hotelds.admin.model.vo.AgentTimeFlowSearchVO;
import com.wormhole.hotelds.admin.service.AgentTimeFlowService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;

/**
 * @Author：flx
 * @Date：2025/5/21 10:18
 * @Description：AgentTimeFlowController
 */
@RestController
@RequestMapping("/agent_time_flow")
public class AgentTimeFlowController {

    @Resource
    private AgentTimeFlowService agentTimeFlowService;

    /**
     * 搜索OTA时间流水列表
     */
    @PostMapping("/search")
    public Mono<Result<PageResult<AgentTimeFlowSearchVO>>> search(@RequestBody AgentTimeFlowSearchReq agentTimeFlowSearchReq) {
        return Mono.zip(
                        agentTimeFlowService.searchCount(agentTimeFlowSearchReq),
                        agentTimeFlowService.search(agentTimeFlowSearchReq)
                )
                .map(tuple -> PageResult.create(tuple.getT1(), tuple.getT2()))
                .flatMap(Result::success);
    }
}
