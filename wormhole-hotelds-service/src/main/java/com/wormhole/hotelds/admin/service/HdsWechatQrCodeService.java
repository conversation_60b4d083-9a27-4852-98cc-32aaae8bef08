package com.wormhole.hotelds.admin.service;

import cn.hutool.extra.spring.SpringUtil;
import com.google.common.base.Preconditions;
import com.google.zxing.*;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.admin.constant.QrCodeConstant;
import com.wormhole.hotelds.admin.model.req.DevicePositionSearchReq;
import com.wormhole.hotelds.admin.model.req.WechatQrCodeDownlandReq;
import com.wormhole.hotelds.admin.model.vo.*;
import com.wormhole.hotelds.admin.repository.DevicePositionRepository;
import com.wormhole.hotelds.admin.repository.HdsWechatQrCodeRepository;
import com.wormhole.hotelds.config.*;
import com.wormhole.hotelds.constant.*;
import com.wormhole.hotelds.core.enums.DeviceTypeEnum;
import com.wormhole.hotelds.core.model.entity.HdsDevicePositionEntity;
import com.wormhole.hotelds.core.model.entity.HdsWechatQrCodeEntity;
import com.wormhole.hotelds.core.model.entity.HdsWechatQrCodeFieldEnum;
import com.wormhole.hotelds.storage.config.BucketProperties;
import com.wormhole.hotelds.util.*;
import com.wormhole.storage.model.StorageParams;
import com.wormhole.storage.service.CosObjectStorageService;
import com.wormhole.storage.service.OssObjectStorageService;
import com.wormhole.wechat.constant.WechatMiniAppConstants;
import com.wormhole.wechat.req.WechatQRCodeReq;
import com.wormhole.wechat.service.WechatMiniAppService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.*;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import javax.imageio.*;
import java.awt.*;
import java.awt.image.*;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;
import java.util.List;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 微信二维码数据表 Service
 *
 * <AUTHOR>
 * @date 2025-04-09 10:51:40
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HdsWechatQrCodeService {

    @Resource
    private HdsWechatQrCodeRepository wechatQrCodeRepository;

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private BucketProperties bucketProperties;

    @Resource
    private OssObjectStorageService ossObjectStorageService;

    @Resource
    private CosObjectStorageService cosObjectStorageService;

    @Resource
    private WechatMiniAppService wechatMiniAppService;

    @Resource
    private DevicePositionRepository devicePositionRepository;

    @Resource
    private DevicePositionService devicePositionService;

    @Resource
    private NormalPosterProperties normalPosterProperties;

    @Resource
    private HotelService hotelService;

    public Mono<String> downlandByPositionCode(String hotelCode, String positionCode, String type, String positionName) {
        log.info("downlandByPositionCode request: {} {} {}", hotelCode, positionCode, type);

        Preconditions.checkArgument(StringUtils.isNoneBlank(hotelCode), "hotelCode must not be blank");
        Preconditions.checkArgument(StringUtils.isNoneBlank(positionCode), "positionCode must not be blank");
        Preconditions.checkArgument(StringUtils.isNoneBlank(type), "type must not be blank");

        boolean isWechat = StringUtils.equals(type, QrCodeTypeEnum.WECHAT.getCode());

        Mono<String> resultMono;
        if (StringUtils.isEmpty(positionName)) {
            // 只查一次 devicePosition，查不到直接报错
            resultMono = devicePositionService.findByPositionCode(positionCode)
                    .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "position not found")))
                    .flatMap(devicePosition -> {
                        if (devicePosition.getId() == null){
                            return Mono.error(new BusinessException(ResultCode.NOT_FOUND, "position not found"));
                        }
                        String posName = devicePosition.getPositionName();
                        if (StringUtils.isEmpty(posName)) {
                            return Mono.error(new BusinessException(ResultCode.NOT_FOUND, "positionName not found"));
                        }
                        return handleQrOrPoster(hotelCode, positionCode, isWechat, posName);
                    });
        } else {
            resultMono = handleQrOrPoster(hotelCode, positionCode, isWechat, positionName);
        }
        return resultMono;
    }

    // 提取公共处理逻辑
    public Mono<String> handleQrOrPoster(String hotelCode, String positionCode, boolean isWechat, String positionName) {
        return wechatQrCodeRepository.findByHotelCodeAndPositionCode(hotelCode, positionCode)
                .switchIfEmpty(getSave(hotelCode, positionCode))
                .flatMap(entity -> isWechat
                        // 处理微信二维码及海报
                        ? handleQRCodeIfNecessary(entity, positionName)
                        // 处理普通二维码及海报
                        : handleNormalPosterIfNecessary(entity, positionName)
                )
                .map(cosObjectStorageService::generatePresignedUrl);
    }

    private Mono<HdsWechatQrCodeEntity> getSave(String hotelCode, String positionCode) {
        return HeaderUtils.getHeaderInfo()
            .flatMap(headerInfo -> 
                wechatQrCodeRepository.save(buildWechatQrCodeEntity(hotelCode, positionCode, headerInfo))
                    .onErrorResume(e -> {
                        // 只捕获唯一约束冲突，兜底查
                        if (e instanceof DuplicateKeyException) {
                            return wechatQrCodeRepository.findByHotelCodeAndPositionCode(hotelCode, positionCode);
                        }
                        return Mono.error(e);
                    })
            );
    }

    public Mono<String> downlandByIds(WechatQrCodeDownlandReq req) {
        log.info("downlandByIds request: {}", JacksonUtils.writeValueAsString(req));

        if (req == null || CollectionUtils.isEmpty(req.getIdList())) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "idList must not be empty"));
        }
        if (StringUtils.isEmpty(req.getType())){
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "type must not be empty"));
        }
        boolean isWechat = StringUtils.equals(req.getType(), QrCodeTypeEnum.WECHAT.getCode());
        String objectKey = String.format(isWechat ? QrCodeConstant.WECHAT_QR_CODE_ZIP_URL : QrCodeConstant.NORMAL_QR_CODE_ZIP_URL, System.currentTimeMillis());

        return devicePositionRepository.findByIdIn(req.getIdList())
                .switchIfEmpty(Flux.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "devicePosition data must not be empty")))
                .flatMap(devicePosition ->
                        wechatQrCodeRepository.findByHotelCodeAndPositionCode(devicePosition.getHotelCode(), devicePosition.getPositionCode())
                                .switchIfEmpty(getSave(devicePosition.getHotelCode(), devicePosition.getPositionCode()))
                                .map(entity -> Tuples.of(devicePosition, entity))
                )
                .flatMap(tuple -> {
                    HdsDevicePositionEntity position = tuple.getT1();
                    HdsWechatQrCodeEntity entity = tuple.getT2();
                    if (isWechat) {
                        // 处理微信二维码
                        return handleQRCodeIfNecessary(entity, position.getPositionName());
                    } else {
                        // 处理普通海报
                        return handleNormalPosterIfNecessary(entity, position.getPositionName());
                    }
                })
                .collectList()
                .flatMap(qrCodeEntities -> {
                    if (CollectionUtils.isEmpty(qrCodeEntities)) {
                        return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "no qrCode data after handle"));
                    }
                    return createZipAndUpload(qrCodeEntities, objectKey);
                })
                .map(cosObjectStorageService::generatePresignedUrl)
                .doOnError(e -> log.error("Download by ids failed", e));
    }

    public void tryAsyncGenerate(HdsDevicePositionEntity entity){
        if (!Objects.equals(DeviceTypeEnum.ROOM.getCode(),entity.getDeviceAppType())) {
            return;
        }
        // 统一获取
        getOrCreateEntity(entity.getHotelCode(), entity.getPositionCode())
                .flatMap(qrEntity -> {
                    // 生成微信二维码
                    Mono<StorageParams> wechatMono = handleQRCodeIfNecessary(qrEntity, entity.getPositionName());
                    // 生成普通二维码和海报
                    Mono<Boolean> posterMono = generateNormalQrCodeAndPoster(entity.getHotelCode(), entity.getPositionCode(),
                            entity.getPositionName(), qrEntity);

                    return Mono.zip(wechatMono, posterMono);
                })
                .subscribeOn(Schedulers.boundedElastic())
                .doOnSuccess(tuple -> log.info("微信和普通海报任务均已触发成功, positionCode={}", entity.getPositionCode()))
                .doOnError(e -> log.error("异步生成二维码或海报失败, positionCode={}", entity.getPositionCode(), e))
                .subscribe();
    }

    /**
     * 查找或创建实体，处理并发竞争问题
     */
    public Mono<HdsWechatQrCodeEntity> getOrCreateEntity(String hotelCode, String positionCode) {
        return wechatQrCodeRepository.findByHotelCodeAndPositionCode(hotelCode, positionCode)
                .switchIfEmpty(getSave(hotelCode, positionCode));
    }

    private Mono<StorageParams> createZipAndUpload(List<StorageParams> qrCodeFiles, String objectKey) {
        return Flux.fromIterable(qrCodeFiles)
                .flatMap(storageParams ->
                        cosObjectStorageService.getObjectAsBytes(storageParams)
                                .map(bytes -> Tuples.of(storageParams, bytes))
                )
                .collectList()
                .flatMap(fileList -> {
                    return Mono.fromCallable(() -> {
                        ByteArrayOutputStream baos = new ByteArrayOutputStream();
                        try (ZipOutputStream zos = new ZipOutputStream(baos)) {
                            for (Tuple2<StorageParams, byte[]> file : fileList) {
                                String fileName = FilenameUtils.getName(file.getT1().getObjectKey());
                                zos.putNextEntry(new ZipEntry(fileName));
                                zos.write(file.getT2());
                                zos.closeEntry();
                            }
                            zos.finish();
                        } catch (IOException e) {
                            log.error("创建 zip 文件失败", e);
                            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "创建 zip 失败");
                        }

                        return new ByteArrayInputStream(baos.toByteArray());
                    });
                })
                .flatMap(inputStream -> {
                    StorageParams zipParams = StorageParams.builder()
                            .bucketName(bucketProperties.getCommonBucketName())
                            .objectKey(objectKey)
                            .build();
                    return cosObjectStorageService.putObject(zipParams, inputStream, null).thenReturn(zipParams);
                });
    }


    private HdsWechatQrCodeEntity buildWechatQrCodeEntity(String hotelCode, String positionCode, HeaderUtils.HeaderInfo headerInfo) {
        HdsWechatQrCodeEntity qrCodeEntity = new HdsWechatQrCodeEntity();
        qrCodeEntity.setHotelCode(hotelCode);
        qrCodeEntity.setPositionCode(positionCode);
        qrCodeEntity.setCreatedAt(LocalDateTime.now());
        qrCodeEntity.setUpdatedAt(LocalDateTime.now());
        qrCodeEntity.setCreatedBy(StringUtils.isBlank(UserUtils.getUserId(headerInfo)) ? "system" : UserUtils.getUserId(headerInfo));
        qrCodeEntity.setCreatedByName(StringUtils.isBlank(UserUtils.getUserName(headerInfo)) ? "system" : UserUtils.getUserId(headerInfo));
        qrCodeEntity.setRowStatus(1);
        log.info("savewechatqrcode qrCodeEntity: {} headerInfo {}", JacksonUtils.writeValueAsString(qrCodeEntity), JacksonUtils.writeValueAsString(headerInfo));
        return qrCodeEntity;
    }

    private Mono<StorageParams> handleNormalPosterIfNecessary(HdsWechatQrCodeEntity entity, String positionName) {
        if (StringUtils.isNotBlank(entity.getNormalPosterUrl())) {
            return Mono.just(StorageParams.builder()
                    .bucketName(bucketProperties.getCommonBucketName())
                    .objectKey(entity.getNormalPosterUrl())
                    .build());
        }
        // 没有海报，先生成再查
        return generateNormalQrCodeAndPoster(entity.getHotelCode(), entity.getPositionCode(), positionName, entity)
                .flatMap(success -> {
                    if (!success) {
                        return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "Failed to generate poster"));
                    }
                    // 重新查库获取最新URL
                    return wechatQrCodeRepository.findByHotelCodeAndPositionCode(entity.getHotelCode(), entity.getPositionCode())
                            .flatMap(newEntity -> {
                                if (StringUtils.isNotBlank(newEntity.getNormalPosterUrl())) {
                                    return Mono.just(StorageParams.builder()
                                            .bucketName(bucketProperties.getCommonBucketName())
                                            .objectKey(newEntity.getNormalPosterUrl())
                                            .build());
                                }
                                return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "Poster not found after generate"));
                            });
                });
    }

    public Mono<StorageParams> handleQRCodeIfNecessary(HdsWechatQrCodeEntity entity, String positionName) {
        if (StringUtils.isNotBlank(entity.getPosterUrl())) {
            StorageParams params = StorageParams.builder()
                    .bucketName(bucketProperties.getCommonBucketName())
                    .objectKey(entity.getPosterUrl())
                    .build();
            return Mono.just(params);
        }
        return generateAndUploadQRCode(entity, positionName);
    }

    private Mono<StorageParams> generateAndUploadQRCode(HdsWechatQrCodeEntity entity, String positionName) {
        return Mono.just(determineEnvVersion())
                .flatMap(envVersion -> generateQRCode(entity.getId(), envVersion))
                .flatMap(qrCode -> processAndUploadQRCode(qrCode, entity, positionName));
    }

    private String determineEnvVersion() {
        String activeProfile = SpringUtil.getActiveProfile();
         if ("test".equals(activeProfile)) {
            return WechatMiniAppConstants.TRIAL_ENV_VERSION;
        } else if ("prod".equals(activeProfile)) {
            return WechatMiniAppConstants.RELEASE_ENV_VERSION;
        }
        return WechatMiniAppConstants.DEV_ENV_VERSION;
    }

    private Mono<byte[]> generateQRCode(Long entityId, String envVersion) {
        WechatQRCodeReq request = WechatQRCodeReq.builder()
                .scene(QrCodeConstant.WECHAT_QR_CODE_URL_PREFIX + entityId.toString())
                .envVersion(envVersion)
                .build();
        return wechatMiniAppService.createQRCode(request);
    }

    private Mono<StorageParams> processAndUploadQRCode(byte[] qrCode, HdsWechatQrCodeEntity entity, String positionName) {
        if (isErrorResponse(qrCode)) {
            String errMsg = new String(qrCode);
            log.error("微信二维码生成失败，返回内容: {}", errMsg);
            return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "微信二维码生成失败"));
        }

        InputStream qrCodeStream = new ByteArrayInputStream(qrCode);
        Mono<StorageParams> qrCodeMono = putObject(QrCodeConstant.WECHAT_QR_CODE_URL, entity.getHotelCode(), positionName, qrCodeStream);
        Mono<StorageParams> posterMono = hotelService.findByHotelCode(entity.getHotelCode())
                .doOnNext(h -> log.info("找到酒店信息: {}", h.getHotelName()))
                .flatMap(hotelInfo -> {
                    try {
                        byte[] posterBytes = generatePosterWithWxQrCode(qrCode, hotelInfo.getHotelName(), positionName);
                        InputStream posterStream = new ByteArrayInputStream(posterBytes);
                        return putObject(QrCodeConstant.WECHAT_QR_CODE_POSTER_URL, entity.getHotelCode(), positionName, posterStream);
                    } catch (IOException e) {
                        log.error("生成微信海报失败", e);
                        return Mono.empty();
                    }
                })
                .onErrorResume(e -> {
                    log.error("处理微信海报过程中出错", e);
                    return Mono.empty();
                });

        return Mono.zip(qrCodeMono, posterMono)
                .flatMap(tuple -> {
                    StorageParams qrCodeParams = tuple.getT1();
                    StorageParams posterParams = tuple.getT2();
                    entity.setQrCodeUrl(qrCodeParams.getObjectKey());
                    entity.setPosterUrl(posterParams.getObjectKey());
                    entity.setUpdatedAt(LocalDateTime.now());
                    return r2dbcEntityTemplate.update(entity).thenReturn(posterParams);
                });
    }

    private Mono<StorageParams> putObject(String formatUrl, String hotelCode, String positionName, InputStream inputStream) {
        String fileName = String.format(formatUrl, hotelCode, positionName, System.currentTimeMillis());
        StorageParams params = StorageParams.builder()
                .bucketName(bucketProperties.getCommonBucketName())
                .objectKey(fileName)
                .build();
        return cosObjectStorageService.putObject(params, inputStream, null)
                .thenReturn(params);
    }

    private boolean isErrorResponse(byte[] data) {
        String content = new String(data);
        return content.contains("errcode");
    }

    public Mono<Long> searchCount(DevicePositionSearchReq req) {
        return devicePositionService.searchCount(req);
    }

    public Mono<List<HdsWechatQrCodeVO>> search(DevicePositionSearchReq req) {
        if (req == null || StringUtils.isBlank(req.getHotelCode())) {
            return Mono.just(Collections.emptyList());
        }

        return devicePositionService.search(req)
                .flatMap(deviceList -> {
                    List<HdsWechatQrCodeVO> voList = deviceList.stream()
                            .map(devicePosition -> {
                                HdsWechatQrCodeVO qrCodeVo = new HdsWechatQrCodeVO();
                                BeanUtils.copyProperties(devicePosition, qrCodeVo);
                                return qrCodeVo;
                            })
                            .toList();

                    if (voList.isEmpty()) {
                        return Mono.just(voList);
                    }

                    Set<String> positionCodes = voList.stream()
                            .map(HdsWechatQrCodeVO::getPositionCode)
                            .collect(Collectors.toSet());

                    return getWechatQrCodeList(req.getHotelCode(), positionCodes)
                            .collectMap(HdsWechatQrCodeEntity::getPositionCode)
                            .map(qrCodeMap -> voList.stream()
                                    .map(vo -> enrichVoWithQrCode(vo, qrCodeMap.get(vo.getPositionCode())))
                                    .collect(Collectors.toList())
                            );
                });
    }


    private Flux<HdsWechatQrCodeEntity> getWechatQrCodeList(String hotelCode, Collection<String> positionCodeList) {
        Criteria criteria = buildBaseCriteria(hotelCode, positionCodeList);
        Query query = Query.query(criteria);
        return r2dbcEntityTemplate.select(query, HdsWechatQrCodeEntity.class);
    }

    /**
     * 构建基础查询条件
     *
     * @param
     * @return
     */
    private Criteria buildBaseCriteria(String hotelCode, Collection<String> positionCodeList) {
        Criteria criteria = Criteria.empty();

        if (CollectionUtils.isNotEmpty(positionCodeList)) {
            criteria = criteria.and(HdsWechatQrCodeFieldEnum.position_code.name()).in(positionCodeList);
        }
        if (StringUtils.isNotBlank(hotelCode)) {
            criteria = criteria.and(HdsWechatQrCodeFieldEnum.hotel_code.name()).is(hotelCode);
        }
        return criteria;
    }

    // 将位置信息添加到VO
    private HdsWechatQrCodeVO enrichVoWithQrCode(HdsWechatQrCodeVO vo, HdsWechatQrCodeEntity qrCode) {
        if (qrCode != null) {
            vo.setQrCodeId(qrCode.getId().toString());
            vo.setQrCodeUrl(qrCode.getQrCodeUrl());
            vo.setPosterUrl(qrCode.getPosterUrl());
            vo.setNormalPosterUrl(qrCode.getNormalPosterUrl());
        }
        return vo;
    }

    /**
     * 生成普通二维码和海报
     */
    public Mono<Boolean> generateNormalQrCodeAndPoster(String hotelCode, String positionCode, String positionName,
                                                       HdsWechatQrCodeEntity qrEntity) {
        // 查询酒店信息和房间号
        Mono<HotelVO> hotelInfoMono = hotelService.findByHotelCode(hotelCode);
        if (StringUtils.isBlank(positionName)) {
            return devicePositionService.findByPositionCode(positionCode)
                    .flatMap(devicePosition -> {
                        if (devicePosition == null || devicePosition.getId() == null) {
                            return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "device position not find"));
                        }
                        String posName = devicePosition.getPositionName();
                        return hotelInfoMono.flatMap(hotelInfo -> doGeneratePoster(qrEntity, hotelInfo, posName));
                    });
        } else {
            return hotelInfoMono.flatMap(hotelInfo -> doGeneratePoster(qrEntity, hotelInfo, positionName));
        }
    }

    private Mono<Boolean> doGeneratePoster(HdsWechatQrCodeEntity entity, HotelVO hotelInfo, String positionName) {
        try {
            // 1. 生成二维码内容
            String qrCodeContent = String.format(normalPosterProperties.getQrRedirectUrl(), entity.getHotelCode(), entity.getPositionCode());
            byte[] qrCodeBytes = QrCodeUtil.generateQrCode(qrCodeContent);
            InputStream qrCodeStream = new ByteArrayInputStream(qrCodeBytes);

            // 2. 上传二维码
            return putObject(QrCodeConstant.NORMAL_QR_CODE_URL, entity.getHotelCode(), positionName, qrCodeStream)
                .flatMap(qrCodeParams -> {
                    // 3. 生成海报（传递酒店名、英文名、房间号）
                    byte[] posterBytes;
                    try {
                        posterBytes = generatePosterWithQrCode(
                            qrCodeBytes,
                            hotelInfo.getHotelName(),
                            hotelInfo.getHotelNamePinYin(),
                            positionName
                        );
                    } catch (IOException e) {
                        log.error("生成海报失败", e);
                        return Mono.just(false);
                    }
                    InputStream posterStream = new ByteArrayInputStream(posterBytes);

                    // 4. 上传海报
                    return putObject(QrCodeConstant.NORMAL_QR_CODE_POSTER_URL, entity.getHotelCode(), positionName, posterStream)
                        .flatMap(posterParams -> {
                            // 5. 更新实体
                            entity.setNormalQrCodeUrl(qrCodeParams.getObjectKey());
                            entity.setNormalPosterUrl(posterParams.getObjectKey());
                            return r2dbcEntityTemplate.update(entity)
                                .thenReturn(true);
                        });
                });
        } catch (Exception e) {
            log.error("生成普通二维码和海报失败", e);
            return Mono.just(false);
        }
    }

    /**
     * 生成带小程序二维码的海报
     */
    private byte[] generatePosterWithWxQrCode(byte[] qrCodeBytes, String hotelNameCn, String roomNo) throws IOException {
        InputStream templateStream = getClass().getClassLoader().getResourceAsStream(normalPosterProperties.getWxTemplatePath());
        if (templateStream == null) {
            throw new IOException("Template image not found: " + normalPosterProperties.getWxTemplatePath());
        }
        BufferedImage template = ImageIO.read(templateStream);
        Graphics2D g2d = template.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // 绘制小程序二维码
        BufferedImage qrCodeImage = ImageIO.read(new ByteArrayInputStream(qrCodeBytes));
        // 计算居中位置
        int qrCodeSize = 580;
        int qrCodeX = (template.getWidth() - qrCodeSize) / 2; // 水平居中
        int qrCodeY = normalPosterProperties.getWxQrcodeY(); // 保持原有Y坐标
        // 绘制调整大小后的二维码
        g2d.drawImage(qrCodeImage, qrCodeX, qrCodeY, qrCodeSize, qrCodeSize, null);

        int imageWidth = template.getWidth();
        int y = 154;

        // 酒店中文名（居中）
        y = drawText(g2d, "· " + hotelNameCn+" ·", "fonts/SourceHanSerifSC-SemiBold.otf", Font.PLAIN, 71, imageWidth, y, "#000000", true, 0);

        // 房间号（固定坐标）
        drawText(g2d, roomNo, "fonts/SourceHanSerifSC-SemiBold.otf", Font.PLAIN, 68, imageWidth, 542, "#000000",
                false, 600);

        g2d.dispose();

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ImageIO.write(template, "PNG", outputStream);
        return outputStream.toByteArray();
    }

    /**
     * 生成带二维码的海报
     */
    private byte[] generatePosterWithQrCode(byte[] qrCodeBytes, String hotelNameCn, String hotelNameEn, String roomNo) throws IOException {
        InputStream templateStream = getClass().getClassLoader().getResourceAsStream(normalPosterProperties.getTemplatePath());
        if (templateStream == null) {
            throw new IOException("Template image not found: " + normalPosterProperties.getTemplatePath());
        }
        BufferedImage template = ImageIO.read(templateStream);
        Graphics2D g2d = template.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // 绘制二维码
        BufferedImage qrCodeImage = ImageIO.read(new ByteArrayInputStream(qrCodeBytes));
        // 计算居中位置
        int qrCodeSize = 768;
        int qrCodeX = (template.getWidth() - qrCodeSize) / 2; // 水平居中
        int qrCodeY = normalPosterProperties.getQrcodeY(); // 保持原有Y坐标
        g2d.drawImage(qrCodeImage, qrCodeX, qrCodeY, qrCodeSize, qrCodeSize, null);

        int imageWidth = template.getWidth();
        int y = normalPosterProperties.getHotelNameY();

        // 酒店中文名（居中）
        y = drawText(g2d, "· " + hotelNameCn+" ·", "fonts/SourceHanSerifSC-SemiBold.otf", Font.PLAIN, 59, imageWidth,
                y, "#000000", true, 0);

        // 酒店英文名（居中）
        drawText(g2d, hotelNameEn, "fonts/SourceHanSerifSC-ExtraLight.otf", Font.PLAIN, 37, imageWidth, y - 20,
                "#000000", true, 0);

        // 房间号（固定坐标）
        drawText(g2d, roomNo, "fonts/SourceHanSerifSC-SemiBold.otf", Font.PLAIN, 84, imageWidth,
                normalPosterProperties.getRoomNumY(), "#000000",
                false, 832);

        g2d.dispose();

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ImageIO.write(template, "PNG", outputStream);
        return outputStream.toByteArray();
    }

    /**
     * 绘制文本，支持居中或指定x坐标，返回下一个Y坐标
     */
    private int drawText(Graphics2D g2d, String text, String fontFilePath, int fontStyle, int fontSize,
                         int imageWidth, int y, String colorHex, boolean center, int xIfNotCenter) {
        try {
            if (StringUtils.isEmpty(text)){
                return y;
            }
            // 优先从缓存获取字体
            Font baseFont = getFontFromCache(fontFilePath);
            Font font = baseFont.deriveFont(fontStyle, (float) fontSize);
            g2d.setFont(font);
            g2d.setColor(Color.decode(colorHex));
            FontMetrics fm = g2d.getFontMetrics(font);
            int x = center ? (imageWidth - fm.stringWidth(text)) / 2 : xIfNotCenter;
            g2d.drawString(text, x, y);
            return y + fm.getHeight();
        } catch (Exception e) {
            log.error("绘制文本失败，text: {}, fontFilePath: {}, fontStyle: {}, fontSize: {}, colorHex: {}, center: {}, xIfNotCenter: {}",
                    text, fontFilePath, fontStyle, fontSize, colorHex, center, xIfNotCenter, e);
            return y;
        }
    }

    private final ConcurrentMap<String, Font> FONT_CACHE = new ConcurrentHashMap<>();

    private Font getFontFromCache(String fontFilePath) throws IOException, FontFormatException {
        return FONT_CACHE.computeIfAbsent(fontFilePath, path -> {
            try (InputStream is = getClass().getClassLoader().getResourceAsStream(fontFilePath)) {
                if (is == null) throw new IOException("Font not found: " + path);
                return Font.createFont(Font.TRUETYPE_FONT, is);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }



}