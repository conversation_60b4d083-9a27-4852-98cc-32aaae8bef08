package com.wormhole.hotelds.admin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：flx
 * @Date：2025/4/1 13:43
 * @Description：HotelSearchVO
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelSearchVO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 门店编号
     */
    private String hotelCode;

    /**
     * 门店名称
     */
    private String hotelName;

    /**
     *
     * 商户简称
     */
    private String merchantShortName;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 协议状态 [0未签约 1合作中 2已过期 3已解约]
     */
    private Integer contractStatus;

    /**
     * 房间数量
     */
    private Integer roomNum;

    /**
     * 设备数量
     */
    private Integer deviceNum;

    /**
     * 服务状态 [0正常运行 1即将过期 2服务暂停 3服务终止]
     */
    private Integer serviceStatus;

    /**
     * 离线设备数量
     */
    private Integer offlineDeviceNum;

    /**
     * AI产品类型
     */
    private List<String> aiProductTypes;

    /**
     *  1-正常, 0-停用 2-未完成初始化
     */
    private Integer status;

    /**
     * ota到期时间
     */
    private String otaEndTime;

    /**
     * 邀请码
     */
    private String invitationCode;

    /**
     * 被邀请码
     */
    private String inviteCode;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 邀请人
     */
    private String invitedBy;

    /**
     * 来源
     */
    private Integer source;
}
