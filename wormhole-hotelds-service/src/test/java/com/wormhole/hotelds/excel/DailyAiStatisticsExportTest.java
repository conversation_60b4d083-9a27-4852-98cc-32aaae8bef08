package com.wormhole.hotelds.excel;

import com.wormhole.hotelds.BaseTest;
import com.wormhole.hotelds.admin.model.enums.BussinessTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.jupiter.api.Assertions.*;

/**
 * @Author: AI Assistant
 * @Date: 2025-08-15
 * @Description: 日报数据导出功能测试
 */
@Slf4j
public class DailyAiStatisticsExportTest extends BaseTest {

    @Autowired
    private DataExportProcessorRegistry dataExportProcessorRegistry;

    @Test
    public void testDailyAiStatisticsProcessorRegistration() {
        // 测试门店日报导出处理器是否正确注册
        String businessType = BussinessTypeEnum.DAILY_AI_STATISTICS.getBusinessType();
        assertTrue(dataExportProcessorRegistry.supports(businessType), 
                "门店日报导出处理器应该被正确注册");
        
        DataExportProcessor<?, ?> processor = dataExportProcessorRegistry.getProcessor(businessType);
        assertNotNull(processor, "应该能够获取到门店日报导出处理器");
        assertEquals(businessType, processor.getBusinessType(), 
                "处理器的业务类型应该匹配");
        
        log.info("门店日报导出处理器注册成功: {}", processor.getClass().getSimpleName());
    }

    @Test
    public void testDailyAiStatisticsGroupProcessorRegistration() {
        // 测试集团日报导出处理器是否正确注册
        String businessType = BussinessTypeEnum.DAILY_AI_STATISTICS_GROUP.getBusinessType();
        assertTrue(dataExportProcessorRegistry.supports(businessType), 
                "集团日报导出处理器应该被正确注册");
        
        DataExportProcessor<?, ?> processor = dataExportProcessorRegistry.getProcessor(businessType);
        assertNotNull(processor, "应该能够获取到集团日报导出处理器");
        assertEquals(businessType, processor.getBusinessType(), 
                "处理器的业务类型应该匹配");
        
        log.info("集团日报导出处理器注册成功: {}", processor.getClass().getSimpleName());
    }

    @Test
    public void testExcelHeaders() {
        // 测试门店日报Excel表头
        String businessType = BussinessTypeEnum.DAILY_AI_STATISTICS.getBusinessType();
        DataExportProcessor<?, ?> processor = dataExportProcessorRegistry.getProcessor(businessType);
        
        var headers = processor.getExcelHeaders();
        assertNotNull(headers, "Excel表头不应该为空");
        assertEquals(11, headers.size(), "门店日报应该有11个表头");
        assertTrue(headers.contains("日期"), "应该包含日期列");
        assertTrue(headers.contains("使用房间数"), "应该包含使用房间数列");
        assertTrue(headers.contains("AI语音通话数"), "应该包含AI语音通话数列");
        
        log.info("门店日报Excel表头: {}", headers);
        
        // 测试集团日报Excel表头
        String groupBusinessType = BussinessTypeEnum.DAILY_AI_STATISTICS_GROUP.getBusinessType();
        DataExportProcessor<?, ?> groupProcessor = dataExportProcessorRegistry.getProcessor(groupBusinessType);
        
        var groupHeaders = groupProcessor.getExcelHeaders();
        assertNotNull(groupHeaders, "Excel表头不应该为空");
        assertEquals(11, groupHeaders.size(), "集团日报应该有11个表头");
        assertTrue(groupHeaders.contains("酒店"), "应该包含酒店列");
        assertTrue(groupHeaders.contains("使用房间数"), "应该包含使用房间数列");
        
        log.info("集团日报Excel表头: {}", groupHeaders);
    }

    @Test
    public void testSupportedBusinessTypes() {
        // 测试所有支持的业务类型
        var supportedTypes = dataExportProcessorRegistry.getSupportedBusinessTypes();
        assertNotNull(supportedTypes, "支持的业务类型列表不应该为空");
        
        assertTrue(supportedTypes.contains(BussinessTypeEnum.DAILY_AI_STATISTICS.getBusinessType()),
                "应该支持门店日报导出");
        assertTrue(supportedTypes.contains(BussinessTypeEnum.DAILY_AI_STATISTICS_GROUP.getBusinessType()),
                "应该支持集团日报导出");
        
        log.info("所有支持的业务类型: {}", supportedTypes);
    }
}
