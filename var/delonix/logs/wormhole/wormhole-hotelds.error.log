2025-07-11 15:18:08.939] [wormhole-hotelds] [main] [dev] [ERROR] [] [] [] [] [] [] [] [] [LoggingFailureAnalysisReporter.java:40] [o.s.b.diagnostics.LoggingFailureAnalysisReporter] 

***************************
APPLICATION FAILED TO START
***************************

Description:

Failed to configure a ConnectionFactory: 'url' attribute is not specified and no embedded database could be configured.

Reason: Failed to determine a suitable R2DBC Connection URL


Action:

Consider the following:
	If you want an embedded database (H2), please put it on the classpath.
	If you have database settings to be loaded from a particular profile you may need to activate it (the profiles dev are currently active).

2025-07-11 15:19:38.518] [wormhole-hotelds] [main] [dev] [ERROR] [] [] [] [] [] [] [] [] [LoggingFailureAnalysisReporter.java:40] [o.s.b.diagnostics.LoggingFailureAnalysisReporter] 

***************************
APPLICATION FAILED TO START
***************************

Description:

Failed to configure a ConnectionFactory: 'url' attribute is not specified and no embedded database could be configured.

Reason: Failed to determine a suitable R2DBC Connection URL


Action:

Consider the following:
	If you want an embedded database (H2), please put it on the classpath.
	If you have database settings to be loaded from a particular profile you may need to activate it (the profiles dev are currently active).

2025-07-11 16:37:52.914] [wormhole-hotelds] [main] [dev] [ERROR] [] [] [] [] [] [] [] [] [LoggingFailureAnalysisReporter.java:40] [o.s.b.diagnostics.LoggingFailureAnalysisReporter] 

***************************
APPLICATION FAILED TO START
***************************

Description:

Failed to configure a ConnectionFactory: 'url' attribute is not specified and no embedded database could be configured.

Reason: Failed to determine a suitable R2DBC Connection URL


Action:

Consider the following:
	If you want an embedded database (H2), please put it on the classpath.
	If you have database settings to be loaded from a particular profile you may need to activate it (the profiles dev are currently active).

2025-07-11 16:38:03.063] [wormhole-hotelds] [main] [dev] [ERROR] [] [] [] [] [] [] [] [] [LoggingFailureAnalysisReporter.java:40] [o.s.b.diagnostics.LoggingFailureAnalysisReporter] 

***************************
APPLICATION FAILED TO START
***************************

Description:

Failed to configure a ConnectionFactory: 'url' attribute is not specified and no embedded database could be configured.

Reason: Failed to determine a suitable R2DBC Connection URL


Action:

Consider the following:
	If you want an embedded database (H2), please put it on the classpath.
	If you have database settings to be loaded from a particular profile you may need to activate it (the profiles dev are currently active).

2025-07-11 16:40:19.159] [wormhole-hotelds] [main] [dev] [ERROR] [] [] [] [] [] [] [] [] [LoggingFailureAnalysisReporter.java:40] [o.s.b.diagnostics.LoggingFailureAnalysisReporter] 

***************************
APPLICATION FAILED TO START
***************************

Description:

Failed to configure a ConnectionFactory: 'url' attribute is not specified and no embedded database could be configured.

Reason: Failed to determine a suitable R2DBC Connection URL


Action:

Consider the following:
	If you want an embedded database (H2), please put it on the classpath.
	If you have database settings to be loaded from a particular profile you may need to activate it (the profiles dev are currently active).

2025-07-11 16:40:51.065] [wormhole-hotelds] [main] [dev] [ERROR] [] [] [] [] [] [] [] [] [LoggingFailureAnalysisReporter.java:40] [o.s.b.diagnostics.LoggingFailureAnalysisReporter] 

***************************
APPLICATION FAILED TO START
***************************

Description:

Failed to configure a ConnectionFactory: 'url' attribute is not specified and no embedded database could be configured.

Reason: Failed to determine a suitable R2DBC Connection URL


Action:

Consider the following:
	If you want an embedded database (H2), please put it on the classpath.
	If you have database settings to be loaded from a particular profile you may need to activate it (the profiles dev are currently active).

2025-07-11 16:41:12.129] [wormhole-hotelds] [main] [dev] [ERROR] [] [] [] [] [] [] [] [] [LoggingFailureAnalysisReporter.java:40] [o.s.b.diagnostics.LoggingFailureAnalysisReporter] 

***************************
APPLICATION FAILED TO START
***************************

Description:

Failed to configure a ConnectionFactory: 'url' attribute is not specified and no embedded database could be configured.

Reason: Failed to determine a suitable R2DBC Connection URL


Action:

Consider the following:
	If you want an embedded database (H2), please put it on the classpath.
	If you have database settings to be loaded from a particular profile you may need to activate it (the profiles dev are currently active).

2025-07-11 16:46:13.279] [wormhole-hotelds] [main] [dev] [ERROR] [] [] [] [] [] [] [] [] [LoggingFailureAnalysisReporter.java:40] [o.s.b.diagnostics.LoggingFailureAnalysisReporter] 

***************************
APPLICATION FAILED TO START
***************************

Description:

Failed to configure a ConnectionFactory: 'url' attribute is not specified and no embedded database could be configured.

Reason: Failed to determine a suitable R2DBC Connection URL


Action:

Consider the following:
	If you want an embedded database (H2), please put it on the classpath.
	If you have database settings to be loaded from a particular profile you may need to activate it (the profiles dev are currently active).

2025-07-11 17:14:04.025] [wormhole-hotelds] [main] [dev] [ERROR] [] [] [] [] [] [] [] [] [LoggingFailureAnalysisReporter.java:40] [o.s.b.diagnostics.LoggingFailureAnalysisReporter] 

***************************
APPLICATION FAILED TO START
***************************

Description:

Failed to configure a ConnectionFactory: 'url' attribute is not specified and no embedded database could be configured.

Reason: Failed to determine a suitable R2DBC Connection URL


Action:

Consider the following:
	If you want an embedded database (H2), please put it on the classpath.
	If you have database settings to be loaded from a particular profile you may need to activate it (the profiles dev are currently active).

2025-07-11 17:14:12.807] [wormhole-hotelds] [main] [dev] [ERROR] [] [] [] [] [] [] [] [] [LoggingFailureAnalysisReporter.java:40] [o.s.b.diagnostics.LoggingFailureAnalysisReporter] 

***************************
APPLICATION FAILED TO START
***************************

Description:

Failed to configure a ConnectionFactory: 'url' attribute is not specified and no embedded database could be configured.

Reason: Failed to determine a suitable R2DBC Connection URL


Action:

Consider the following:
	If you want an embedded database (H2), please put it on the classpath.
	If you have database settings to be loaded from a particular profile you may need to activate it (the profiles dev are currently active).

2025-07-11 17:14:33.694] [wormhole-hotelds] [main] [dev] [ERROR] [] [] [] [] [] [] [] [] [LoggingFailureAnalysisReporter.java:40] [o.s.b.diagnostics.LoggingFailureAnalysisReporter] 

***************************
APPLICATION FAILED TO START
***************************

Description:

Failed to configure a ConnectionFactory: 'url' attribute is not specified and no embedded database could be configured.

Reason: Failed to determine a suitable R2DBC Connection URL


Action:

Consider the following:
	If you want an embedded database (H2), please put it on the classpath.
	If you have database settings to be loaded from a particular profile you may need to activate it (the profiles dev are currently active).

